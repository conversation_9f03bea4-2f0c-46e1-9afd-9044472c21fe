/* Enhanced Animations for Wizlop Landing Page */

/* Floating animation for orbs */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(-10px) translateX(-5px);
  }
  75% {
    transform: translateY(-30px) translateX(15px);
  }
}

/* Glow animation for particles */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px currentColor;
    opacity: 0.8;
  }
  100% {
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
    opacity: 1;
  }
}

/* Fade in animation */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide up animation */
@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced pulse animation */
@keyframes enhanced-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Shimmer animation for text */
@keyframes shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Bounce with glow */
@keyframes bounce-glow {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
    box-shadow: 0 0 5px currentColor;
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
    box-shadow: 0 0 15px currentColor, 0 0 25px currentColor;
  }
  70% {
    transform: translate3d(0, -15px, 0);
    box-shadow: 0 0 10px currentColor, 0 0 20px currentColor;
  }
  90% {
    transform: translate3d(0, -4px, 0);
    box-shadow: 0 0 8px currentColor;
  }
}

/* Utility classes */
.animate-fade-in {
  animation: fade-in 1s ease-out;
}

.animate-slide-up {
  animation: slide-up 1s ease-out;
}

.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-150 {
  animation-delay: 0.15s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-400 {
  animation-delay: 0.4s;
}

/* Hover effects */
.hover-glow:hover {
  animation: glow 0.3s ease-in-out;
}

.hover-float:hover {
  animation: float 2s ease-in-out infinite;
}

/* Responsive animations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  /* Disable complex animations for reduced motion */
  .float, .bounce, .pulse, .spin {
    animation: none !important;
  }

  /* Keep essential transitions but make them faster */
  .hover\:scale-105:hover,
  .hover\:scale-110:hover {
    transform: scale(1.02) !important;
    transition-duration: 0.1s !important;
  }
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* Optimize for 60fps animations */
.smooth-60fps {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Lazy loading optimization */
.lazy-load {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.lazy-load.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Critical CSS for above-the-fold content */
.critical-content {
  contain: layout style paint;
}

/* Non-critical content optimization */
.non-critical {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* Glass morphism enhancement */
.glass-enhanced {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.5);
}

/* Gradient text animation */
.gradient-text-animated {
  background: linear-gradient(
    -45deg,
    #ee7752,
    #e73c7e,
    #23a6d5,
    #23d5ab
  );
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Particle trail effect */
.particle-trail::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: currentColor;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: particle-trail 2s linear infinite;
}

@keyframes particle-trail {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0) translateY(-100px);
  }
}

/* Enhanced button ripple */
.button-ripple {
  position: relative;
  overflow: hidden;
}

.button-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.button-ripple:hover::after {
  width: 300px;
  height: 300px;
}

/* Magnetic hover effect */
.magnetic-hover {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic-hover:hover {
  transform: translateY(-8px) scale(1.02);
}

/* Stagger animation for lists */
.stagger-animation > * {
  opacity: 0;
  transform: translateY(20px);
  animation: slide-up 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

/* Advanced Interactive Elements */

/* Morphing button animation */
@keyframes morph {
  0%, 100% {
    border-radius: 50px;
  }
  25% {
    border-radius: 20px 50px 20px 50px;
  }
  50% {
    border-radius: 10px;
  }
  75% {
    border-radius: 50px 20px 50px 20px;
  }
}

.morph-hover:hover {
  animation: morph 2s ease-in-out infinite;
}

/* Liquid button effect */
@keyframes liquid {
  0% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.1) rotate(180deg);
  }
  100% {
    transform: scale(1) rotate(360deg);
  }
}

.liquid-effect {
  position: relative;
  overflow: hidden;
}

.liquid-effect::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  animation: liquid 4s ease-in-out infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.liquid-effect:hover::before {
  opacity: 1;
}

/* Parallax scroll effect */
.parallax-element {
  transform: translateZ(0);
  will-change: transform;
}

/* 3D card flip effect */
.card-3d {
  perspective: 1000px;
}

.card-3d-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.card-3d:hover .card-3d-inner {
  transform: rotateY(180deg);
}

.card-3d-front, .card-3d-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 20px;
}

.card-3d-back {
  transform: rotateY(180deg);
}

/* Elastic scale animation */
@keyframes elastic-scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.elastic-hover:hover {
  animation: elastic-scale 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Text reveal animation */
@keyframes text-reveal {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.text-reveal {
  position: relative;
  overflow: hidden;
}

.text-reveal::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
  animation: text-reveal 2s ease-in-out infinite;
}

/* Floating action button */
@keyframes fab-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(33, 194, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(33, 194, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(33, 194, 255, 0);
  }
}

.fab-pulse {
  animation: fab-pulse 2s infinite;
}

/* Typewriter effect */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: currentColor;
  }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid;
  white-space: nowrap;
  margin: 0 auto;
  animation:
    typewriter 3.5s steps(40, end),
    blink-caret 0.75s step-end infinite;
}

/* Magnetic attraction effect */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic:hover {
  transform: translateY(-10px) scale(1.05);
}

/* Neon glow effect */
@keyframes neon-glow {
  0%, 100% {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor;
  }
  50% {
    text-shadow:
      0 0 10px currentColor,
      0 0 20px currentColor,
      0 0 30px currentColor,
      0 0 40px currentColor;
  }
}

.neon-text {
  animation: neon-glow 2s ease-in-out infinite alternate;
}

/* Smooth reveal on scroll */
.reveal-on-scroll {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.reveal-on-scroll.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Loading skeleton animation */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Micro-interactions */
.micro-bounce:hover {
  animation: bounce 0.6s ease-in-out;
}

.micro-shake:hover {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive Design Enhancements */

/* Mobile-first responsive typography */
.responsive-text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.responsive-text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.responsive-text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.responsive-text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.responsive-text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.responsive-text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.responsive-text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.responsive-text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.responsive-text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.responsive-text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

/* Tablet adjustments */
@media (min-width: 768px) {
  .responsive-text-lg {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .responsive-text-xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .responsive-text-2xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .responsive-text-3xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .responsive-text-4xl {
    font-size: 3rem;
    line-height: 1;
  }

  .responsive-text-5xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .responsive-text-6xl {
    font-size: 4.5rem;
    line-height: 1;
  }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
  .responsive-text-4xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .responsive-text-5xl {
    font-size: 4.5rem;
    line-height: 1;
  }

  .responsive-text-6xl {
    font-size: 6rem;
    line-height: 1;
  }
}

/* Responsive spacing utilities */
.responsive-padding-sm {
  padding: 1rem;
}

.responsive-padding-md {
  padding: 1.5rem;
}

.responsive-padding-lg {
  padding: 2rem;
}

@media (min-width: 768px) {
  .responsive-padding-sm {
    padding: 1.5rem;
  }

  .responsive-padding-md {
    padding: 2rem;
  }

  .responsive-padding-lg {
    padding: 3rem;
  }
}

@media (min-width: 1024px) {
  .responsive-padding-sm {
    padding: 2rem;
  }

  .responsive-padding-md {
    padding: 3rem;
  }

  .responsive-padding-lg {
    padding: 4rem;
  }
}

/* Responsive grid utilities */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .responsive-grid {
    gap: 1.5rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    gap: 2rem;
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    gap: 2.5rem;
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Mobile-optimized animations */
@media (max-width: 767px) {
  .mobile-reduced-motion {
    animation-duration: 0.5s !important;
    transition-duration: 0.3s !important;
  }

  .mobile-no-transform:hover {
    transform: none !important;
  }

  .mobile-simplified-shadow {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .touch-friendly {
    min-height: 44px;
    min-width: 44px;
  }

  .touch-friendly-padding {
    padding: 12px 16px;
  }

  /* Disable hover effects on touch devices */
  .no-touch-hover:hover {
    transform: none !important;
    box-shadow: inherit !important;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-optimized {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dark-mode-adaptive {
    filter: brightness(0.9) contrast(1.1);
  }
}

/* Landscape mobile optimizations */
@media (max-height: 500px) and (orientation: landscape) {
  .landscape-mobile-compact {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .landscape-mobile-text {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
  }
}
