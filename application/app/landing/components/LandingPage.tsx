/** @format */

import { colors, gradients } from '@/app/colors';
import React from 'react';
import FeaturesSection from './FeaturesSection';
import Footer from './Footer';
import HeroSection from './HeroSection';

interface LandingPageProps {
	onGetStarted: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
	return (
		<div
			className='min-h-screen relative'
			style={{
				background: gradients.cosmic,
			}}
			role='document'
			aria-label='Wizlop landing page'>
			{/* Enhanced Navigation */}
			<nav
				className='fixed top-0 left-0 right-0 z-50 transition-all duration-500 py-4'
				role='navigation'
				aria-label='Main navigation'>
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
					<div className='flex items-center justify-between'>
						<div className='flex items-center space-x-3 group cursor-pointer'>
							<div
								className='w-12 h-12 rounded-2xl flex items-center justify-center transition-all duration-500 group-hover:scale-110 group-hover:rotate-12'
								style={{
									background: gradients.trustGradient,
								}}>
								<span className='text-white font-bold'>W</span>
							</div>
							<span
								className='text-2xl font-black text-transparent bg-clip-text'
								style={{
									backgroundImage: gradients.trustGradient,
								}}>
								WIZLOP
							</span>
						</div>

						<button
							onClick={onGetStarted}
							className='px-8 py-3 rounded-full font-bold text-white transition-all duration-500 hover:scale-105 touch-friendly'
							style={{
								background: gradients.adventureGradient,
							}}
							aria-label='Get started with Wizlop'
							role='button'
							tabIndex={0}>
							Get Started
						</button>
					</div>
				</div>
			</nav>

			{/* Floating Background Elements */}
			<div className='absolute inset-0 overflow-hidden pointer-events-none'>
				{Array.from({ length: 8 }, (_, i) => (
					<div
						key={i}
						className='absolute rounded-full opacity-10'
						style={{
							width: `${Math.random() * 8 + 4}px`,
							height: `${Math.random() * 8 + 4}px`,
							left: `${Math.random() * 100}%`,
							top: `${Math.random() * 100}%`,
							backgroundColor: ['#3B82F6', '#8B5CF6', '#06B6D4', '#10B981'][
								Math.floor(Math.random() * 4)
							],
							animation: `float ${
								Math.random() * 10 + 10
							}s ease-in-out infinite`,
							animationDelay: `${Math.random() * 5}s`,
						}}></div>
				))}
			</div>

			{/* Main Content */}
			<HeroSection onGetStarted={onGetStarted} />
			{/* Company Values Section */}
			<section
				className='py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden'
				aria-labelledby='company-values-heading'>
				<div className='absolute inset-0'>
					<div
						className='absolute top-10 right-10 w-48 h-48 rounded-full opacity-10 blur-2xl'
						style={{
							background: gradients.holographic,
							animation: 'float 8s ease-in-out infinite',
						}}></div>
				</div>

				<div className='relative max-w-6xl mx-auto text-center'>
					<div className='mb-12'>
						<div
							className='inline-flex items-center space-x-3 rounded-full px-6 py-3 backdrop-blur-md border mb-8'
							style={{
								background: colors.glass.light,
								borderColor: colors.psychological.trust,
								boxShadow: `0 0 20px ${colors.psychological.trust}20`,
							}}>
							<span
								className='text-sm font-bold tracking-wider'
								style={{ color: colors.psychological.trust }}>
								OUR VISION
							</span>
						</div>

						<h2
							id='company-values-heading'
							className='text-4xl md:text-5xl font-black mb-6 leading-tight'>
							<span style={{ color: colors.neutral.textBlack }}>
								Making the World
							</span>
							<br />
							<span
								className='text-transparent bg-clip-text'
								style={{
									backgroundImage: gradients.adventureGradient,
								}}>
								Accessible to Everyone
							</span>
						</h2>
					</div>

					<div className='grid md:grid-cols-3 gap-8'>
						{[
							{
								title: 'Effortless Discovery',
								description:
									'No complex searches or technical barriers. Just natural conversation that understands your intent.',
								color: colors.psychological.innovation,
								gradient: gradients.innovationGradient,
							},
							{
								title: 'Personal & Magical',
								description:
									'Every recommendation is tailored to your unique preferences and travel style.',
								color: colors.psychological.luxury,
								gradient: gradients.luxuryGradient,
							},
							{
								title: 'Global Accessibility',
								description:
									'Breaking down language and cultural barriers to make every destination discoverable.',
								color: colors.psychological.adventure,
								gradient: gradients.adventureGradient,
							},
						].map((value, index) => (
							<div
								key={index}
								className='p-8 rounded-3xl backdrop-blur-md border transition-all duration-500 hover:scale-105'
								style={{
									background: `linear-gradient(135deg, ${colors.glass.light}, ${value.color}10)`,
									borderColor: value.color,
									boxShadow: `0 0 30px ${value.color}15`,
								}}>
								<div
									className='w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center'
									style={{
										background: value.gradient,
									}}>
									<span className='text-2xl'>✨</span>
								</div>
								<h3
									className='text-xl font-bold mb-4 text-transparent bg-clip-text'
									style={{
										backgroundImage: value.gradient,
									}}>
									{value.title}
								</h3>
								<p
									className='leading-relaxed'
									style={{ color: colors.neutral.textBlack }}>
									{value.description}
								</p>
							</div>
						))}
					</div>
				</div>
			</section>

			<div
				id='how-it-works'
				className='py-12 bg-transparent'>
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
					<div className='text-center mb-16'>
						<h2
							className='text-4xl md:text-5xl font-bold mb-6'
							style={{ color: colors.neutral.textBlack }}>
							How Wizlop Works
						</h2>
						<p
							className='text-xl leading-relaxed max-w-3xl mx-auto'
							style={{ color: colors.neutral.slateGray }}>
							Experience the future of location discovery through natural
							conversation and AI intelligence.
						</p>
					</div>
				</div>
			</div>
			<FeaturesSection />
			<Footer />
		</div>
	);
};

export default LandingPage;
