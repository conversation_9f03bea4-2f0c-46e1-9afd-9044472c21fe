/** @format */

import { gradients } from '@/app/colors';
import React from 'react';
import FeaturesSection from './FeaturesSection';
import Footer from './Footer';
import HeroSection from './HeroSection';

interface LandingPageProps {
	onGetStarted: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
	return (
		<div
			className='min-h-screen relative'
			style={{
				background: gradients.lightMesh,
			}}>
			{/* Main Content */}
			<HeroSection onGetStarted={onGetStarted} />
			<FeaturesSection />
			<Footer />
		</div>
	);
};

export default LandingPage;
