/** @format */

import { colors, gradients, shadows } from '@/app/colors';
import React, { useState, useEffect } from 'react';
import { FiMenu, FiX, FiGlobe, FiUser, FiSettings, FiHeart } from 'react-icons/fi';
import './animations.css';

interface InteractiveNavigationProps {
  onGetStarted: () => void;
}

const InteractiveNavigation: React.FC<InteractiveNavigationProps> = ({ onGetStarted }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { label: 'Features', href: '#features', icon: FiGlobe },
    { label: 'How it Works', href: '#how-it-works', icon: FiSettings },
    { label: 'About', href: '#about', icon: FiHeart },
  ];

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        scrolled ? 'py-2' : 'py-4'
      }`}
      style={{
        background: scrolled 
          ? `linear-gradient(135deg, ${colors.glass.backdrop}, ${colors.glass.blueGlass})`
          : 'transparent',
        backdropFilter: scrolled ? 'blur(20px)' : 'none',
        borderBottom: scrolled ? `1px solid ${colors.glass.medium}` : 'none',
      }}>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between">
          
          {/* Logo */}
          <div className="flex items-center space-x-3 group cursor-pointer">
            <div
              className="w-12 h-12 rounded-2xl flex items-center justify-center transition-all duration-500 group-hover:scale-110 group-hover:rotate-12"
              style={{
                background: gradients.trustGradient,
                boxShadow: `0 0 20px ${colors.psychological.innovation}30`,
              }}>
              <FiGlobe className="w-6 h-6 text-white" />
            </div>
            <span
              className="text-2xl font-black text-transparent bg-clip-text"
              style={{
                backgroundImage: gradients.trustGradient,
              }}>
              WIZLOP
            </span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item, index) => (
              <a
                key={index}
                href={item.href}
                className="group relative px-4 py-2 rounded-full transition-all duration-300 hover:scale-105"
                style={{
                  color: colors.neutral.textBlack,
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = colors.glass.light;
                  e.currentTarget.style.boxShadow = `0 0 20px ${colors.psychological.innovation}20`;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'transparent';
                  e.currentTarget.style.boxShadow = 'none';
                }}>
                <div className="flex items-center space-x-2">
                  <item.icon className="w-4 h-4 group-hover:rotate-12 transition-transform duration-300" />
                  <span className="font-medium">{item.label}</span>
                </div>
                
                {/* Hover underline */}
                <div
                  className="absolute bottom-0 left-1/2 w-0 h-0.5 group-hover:w-full group-hover:left-0 transition-all duration-300"
                  style={{
                    background: gradients.electric,
                  }}></div>
              </a>
            ))}
          </div>

          {/* CTA Button */}
          <div className="hidden md:block">
            <button
              onClick={onGetStarted}
              className="group relative px-8 py-3 rounded-full font-bold text-white transition-all duration-500 hover:scale-105 liquid-effect overflow-hidden"
              style={{
                background: gradients.adventureGradient,
                boxShadow: `0 0 30px ${colors.psychological.adventure}40`,
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.boxShadow = `0 0 50px ${colors.psychological.adventure}60`;
                e.currentTarget.style.transform = 'scale(1.05) translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.boxShadow = `0 0 30px ${colors.psychological.adventure}40`;
                e.currentTarget.style.transform = 'scale(1)';
              }}>
              
              <span className="relative z-10 flex items-center space-x-2">
                <span>Get Started</span>
                <div className="w-2 h-2 rounded-full bg-white opacity-80 group-hover:animate-bounce"></div>
              </span>
              
              {/* Ripple effect */}
              <div
                className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-30 transition-opacity duration-500"
                style={{
                  background: `radial-gradient(circle, ${colors.psychological.energy} 0%, transparent 70%)`,
                  animation: 'ping 1s cubic-bezier(0, 0, 0.2, 1) infinite',
                }}></div>
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-xl transition-all duration-300 hover:scale-110"
            style={{
              background: colors.glass.light,
              color: colors.psychological.innovation,
            }}>
            {isMenuOpen ? <FiX className="w-6 h-6" /> : <FiMenu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        <div
          className={`md:hidden transition-all duration-500 overflow-hidden ${
            isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          }`}>
          <div
            className="mt-4 p-6 rounded-3xl backdrop-blur-md border"
            style={{
              background: colors.glass.light,
              borderColor: colors.glass.medium,
            }}>
            
            {/* Mobile Navigation Items */}
            <div className="space-y-4">
              {navItems.map((item, index) => (
                <a
                  key={index}
                  href={item.href}
                  className="flex items-center space-x-3 p-3 rounded-2xl transition-all duration-300 hover:scale-105"
                  style={{
                    color: colors.neutral.textBlack,
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = colors.glass.blueGlass;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'transparent';
                  }}
                  onClick={() => setIsMenuOpen(false)}>
                  <item.icon className="w-5 h-5" style={{ color: colors.psychological.innovation }} />
                  <span className="font-medium">{item.label}</span>
                </a>
              ))}
            </div>

            {/* Mobile CTA */}
            <button
              onClick={() => {
                onGetStarted();
                setIsMenuOpen(false);
              }}
              className="w-full mt-6 px-6 py-4 rounded-2xl font-bold text-white transition-all duration-300 hover:scale-105"
              style={{
                background: gradients.adventureGradient,
                boxShadow: `0 0 20px ${colors.psychological.adventure}30`,
              }}>
              Get Started
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default InteractiveNavigation;
