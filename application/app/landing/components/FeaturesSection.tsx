/** @format */

import { colors, gradients, shadows } from '@/app/colors';
import React from 'react';
import {
	FiCamera,
	FiGlobe,
	FiStar,
	FiTrendingUp,
	FiUsers,
} from 'react-icons/fi';
import './animations.css';

const FeaturesSection: React.FC = () => {
	return (
		<div className='relative py-24 px-4 sm:px-6 lg:px-8 overflow-hidden'>
			{/* Enhanced Background Elements */}
			<div className='absolute inset-0'>
				<div
					className='absolute top-20 left-10 w-64 h-64 rounded-full opacity-10 blur-3xl'
					style={{
						background: gradients.innovationGradient,
						animation: 'float 8s ease-in-out infinite',
					}}></div>
				<div
					className='absolute bottom-20 right-10 w-48 h-48 rounded-full opacity-15 blur-2xl'
					style={{
						background: gradients.adventureGradient,
						animation: 'float 10s ease-in-out infinite reverse',
						animationDelay: '2s',
					}}></div>
			</div>

			<div className='relative max-w-7xl mx-auto'>
				{/* Enhanced Section Header */}
				<div className='text-center mb-20'>
					{/* Section Badge */}
					<div className='animate-fade-in mb-8'>
						<div
							className='inline-flex items-center space-x-3 rounded-full px-6 py-3 backdrop-blur-md border'
							style={{
								background: colors.glass.light,
								borderColor: colors.psychological.innovation,
								boxShadow: `0 0 20px ${colors.psychological.innovation}20`,
							}}>
							<FiZap
								className='w-5 h-5'
								style={{ color: colors.psychological.innovation }}
							/>
							<span
								className='text-sm font-bold tracking-wider'
								style={{ color: colors.psychological.innovation }}>
								POWERFUL FEATURES
							</span>
						</div>
					</div>

					<div className='animate-slide-up'>
						<h2 className='text-5xl md:text-6xl font-black mb-8 leading-tight'>
							<span style={{ color: colors.neutral.textBlack }}>
								Everything You Need to
							</span>
							<br />
							<span
								className='text-transparent bg-clip-text'
								style={{
									backgroundImage: gradients.electric,
									textShadow: `0 0 40px ${colors.psychological.energy}30`,
								}}>
								Explore the World
							</span>
						</h2>
						<div
							className='max-w-4xl mx-auto p-6 rounded-2xl backdrop-blur-sm border'
							style={{
								background: `linear-gradient(135deg, ${colors.glass.light}, ${colors.glass.blueGlass})`,
								borderColor: colors.glass.medium,
							}}>
							<p
								className='text-xl leading-relaxed'
								style={{ color: colors.neutral.textBlack }}>
								From{' '}
								<span
									className='font-semibold text-transparent bg-clip-text'
									style={{ backgroundImage: gradients.innovationGradient }}>
									interactive 3D globes
								</span>{' '}
								to{' '}
								<span
									className='font-semibold text-transparent bg-clip-text'
									style={{ backgroundImage: gradients.adventureGradient }}>
									AI-powered recommendations
								</span>
								, Wizlop provides all the tools you need for unforgettable
								discoveries.
							</p>
						</div>
					</div>
				</div>

				{/* Enhanced Features Grid */}
				<div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8 stagger-animation'>
					{/* Interactive Globe */}
					<div
						className='group relative p-10 rounded-3xl backdrop-blur-md border-2 transition-all duration-500 hover:scale-105 overflow-hidden'
						style={{
							background: `linear-gradient(135deg, ${colors.glass.light}, ${colors.glass.blueGlass})`,
							borderColor: colors.psychological.innovation,
							boxShadow: `${shadows.glass}, 0 0 30px ${colors.psychological.innovation}15`,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.boxShadow = `${shadows.glass}, 0 0 50px ${colors.psychological.innovation}30`;
							e.currentTarget.style.transform = 'scale(1.05) translateY(-8px)';
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.boxShadow = `${shadows.glass}, 0 0 30px ${colors.psychological.innovation}15`;
							e.currentTarget.style.transform = 'scale(1)';
						}}>
						{/* Background Pattern */}
						<div className='absolute inset-0 opacity-5'>
							<div
								className='absolute inset-0'
								style={{
									backgroundImage: `radial-gradient(circle at 20% 20%, ${colors.psychological.innovation} 1px, transparent 1px)`,
									backgroundSize: '20px 20px',
								}}></div>
						</div>

						<div className='relative'>
							<div
								className='w-20 h-20 mb-8 rounded-3xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-500'
								style={{
									background: gradients.electric,
									boxShadow: `0 0 20px ${colors.psychological.innovation}40`,
								}}>
								<FiGlobe className='w-10 h-10 text-white' />
								<div
									className='absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500'
									style={{
										background: `radial-gradient(circle, ${colors.psychological.innovation}30, transparent)`,
									}}></div>
							</div>
							<h3
								className='text-2xl font-bold mb-6 text-transparent bg-clip-text'
								style={{
									backgroundImage: `linear-gradient(135deg, ${colors.psychological.innovation}, ${colors.psychological.energy})`,
								}}>
								Interactive 3D Globe
							</h3>
							<p
								className='leading-relaxed text-base'
								style={{ color: colors.neutral.textBlack }}>
								Explore the world in stunning 3D. Seamlessly zoom from global
								view to street-level detail, discover city rankings, and
								navigate with an intuitive interface that makes geography come
								alive.
							</p>

							{/* Feature highlights */}
							<div className='mt-6 flex flex-wrap gap-2'>
								{['3D Navigation', 'Real-time Data', 'Smooth Transitions'].map(
									(tag, index) => (
										<span
											key={index}
											className='px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm border'
											style={{
												background: colors.glass.light,
												borderColor: colors.psychological.innovation,
												color: colors.psychological.innovation,
											}}>
											{tag}
										</span>
									)
								)}
							</div>
						</div>

						{/* Decorative corner element */}
						<div
							className='absolute top-4 right-4 w-3 h-3 rounded-full opacity-60'
							style={{
								background: colors.psychological.innovation,
								animation: 'pulse 2s ease-in-out infinite',
							}}></div>
					</div>

					{/* AI-Powered Conversations */}
					<div
						className='group relative p-10 rounded-3xl backdrop-blur-md border-2 transition-all duration-500 hover:scale-105 overflow-hidden'
						style={{
							background: `linear-gradient(135deg, ${colors.glass.light}, ${colors.glass.greenGlass})`,
							borderColor: colors.psychological.adventure,
							boxShadow: `${shadows.glass}, 0 0 30px ${colors.psychological.adventure}15`,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.boxShadow = `${shadows.glass}, 0 0 50px ${colors.psychological.adventure}30`;
							e.currentTarget.style.transform = 'scale(1.05) translateY(-8px)';
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.boxShadow = `${shadows.glass}, 0 0 30px ${colors.psychological.adventure}15`;
							e.currentTarget.style.transform = 'scale(1)';
						}}>
						{/* Background Pattern */}
						<div className='absolute inset-0 opacity-5'>
							<div
								className='absolute inset-0'
								style={{
									backgroundImage: `radial-gradient(circle at 80% 20%, ${colors.psychological.adventure} 1px, transparent 1px)`,
									backgroundSize: '25px 25px',
								}}></div>
						</div>

						<div className='relative'>
							<div
								className='w-20 h-20 mb-8 rounded-3xl flex items-center justify-center group-hover:scale-110 group-hover:-rotate-6 transition-all duration-500'
								style={{
									background: gradients.adventureGradient,
									boxShadow: `0 0 20px ${colors.psychological.adventure}40`,
								}}>
								<FiUsers className='w-10 h-10 text-white' />
								<div
									className='absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500'
									style={{
										background: `radial-gradient(circle, ${colors.psychological.adventure}30, transparent)`,
									}}></div>
							</div>
							<h3
								className='text-2xl font-bold mb-6 text-transparent bg-clip-text'
								style={{
									backgroundImage: `linear-gradient(135deg, ${colors.psychological.adventure}, ${colors.psychological.growth})`,
								}}>
								AI-Powered Conversations
							</h3>
							<p
								className='leading-relaxed text-base'
								style={{ color: colors.neutral.textBlack }}>
								Chat naturally with our advanced AI that understands context,
								remembers your preferences, and provides personalized
								recommendations based on your unique travel style.
							</p>

							{/* Feature highlights */}
							<div className='mt-6 flex flex-wrap gap-2'>
								{[
									'Natural Language',
									'Context Memory',
									'Personal Insights',
								].map((tag, index) => (
									<span
										key={index}
										className='px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm border'
										style={{
											background: colors.glass.light,
											borderColor: colors.psychological.adventure,
											color: colors.psychological.adventure,
										}}>
										{tag}
									</span>
								))}
							</div>
						</div>

						{/* Decorative corner element */}
						<div
							className='absolute top-4 right-4 w-3 h-3 rounded-full opacity-60'
							style={{
								background: colors.psychological.adventure,
								animation: 'pulse 2.5s ease-in-out infinite',
								animationDelay: '0.5s',
							}}></div>
					</div>

					{/* POI Profiles */}
					<div
						className='p-8 rounded-3xl backdrop-blur-md border hover:scale-105 transition-all duration-300'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
							boxShadow: shadows.glass,
						}}>
						<div
							className='w-16 h-16 mb-6 rounded-2xl flex items-center justify-center'
							style={{
								background: gradients.sunset,
							}}>
							<FiStar className='w-8 h-8 text-white' />
						</div>
						<h3
							className='text-2xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							POI Profiles
						</h3>
						<p
							className='leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Comprehensive profiles for every point of interest. Read reviews,
							view photos, check ratings, and get all the details you need.
						</p>
					</div>

					{/* Photo Gallery */}
					<div
						className='p-8 rounded-3xl backdrop-blur-md border hover:scale-105 transition-all duration-300'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
							boxShadow: shadows.glass,
						}}>
						<div
							className='w-16 h-16 mb-6 rounded-2xl flex items-center justify-center'
							style={{
								background: gradients.electric,
							}}>
							<FiCamera className='w-8 h-8 text-white' />
						</div>
						<h3
							className='text-2xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Rich Media
						</h3>
						<p
							className='leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Browse high-quality photos and media for every location. See what
							places really look like before you visit.
						</p>
					</div>

					{/* Community Reviews */}
					<div
						className='p-8 rounded-3xl backdrop-blur-md border hover:scale-105 transition-all duration-300'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
							boxShadow: shadows.glass,
						}}>
						<div
							className='w-16 h-16 mb-6 rounded-2xl flex items-center justify-center'
							style={{
								background: gradients.neon,
							}}>
							<FiUsers className='w-8 h-8 text-white' />
						</div>
						<h3
							className='text-2xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Community Insights
						</h3>
						<p
							className='leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Real reviews from real travelers. Get authentic insights and
							recommendations from people who've been there.
						</p>
					</div>

					{/* Smart Rankings */}
					<div
						className='p-8 rounded-3xl backdrop-blur-md border hover:scale-105 transition-all duration-300'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
							boxShadow: shadows.glass,
						}}>
						<div
							className='w-16 h-16 mb-6 rounded-2xl flex items-center justify-center'
							style={{
								background: gradients.sunset,
							}}>
							<FiTrendingUp className='w-8 h-8 text-white' />
						</div>
						<h3
							className='text-2xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Smart Rankings
						</h3>
						<p
							className='leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Discover trending locations and top-rated spots. Our intelligent
							ranking system helps you find the best places to visit.
						</p>
					</div>
				</div>

				{/* Bottom CTA */}
				<div className='text-center mt-16'>
					<p
						className='text-lg mb-6'
						style={{ color: colors.neutral.slateGray }}>
						Ready to discover your next adventure?
					</p>
					<div
						className='inline-flex items-center space-x-2 px-6 py-3 rounded-full backdrop-blur-md border'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
						}}>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							🇹🇷 Turkey available now • More countries coming soon
						</span>
					</div>
				</div>
			</div>
		</div>
	);
};

export default FeaturesSection;
