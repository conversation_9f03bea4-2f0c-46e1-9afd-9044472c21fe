/** @format */

import { colors, gradients, shadows } from '@/app/colors';
import React from 'react';
import {
	FiMap,
	FiStar,
	FiCamera,
	FiUsers,
	FiTrendingUp,
	FiGlobe,
} from 'react-icons/fi';

const FeaturesSection: React.FC = () => {
	return (
		<div className='relative py-20 px-4 sm:px-6 lg:px-8'>
			<div className='max-w-7xl mx-auto'>
				{/* Section Header */}
				<div className='text-center mb-16'>
					<h2 
						className='text-4xl md:text-5xl font-bold mb-6'
						style={{ color: colors.neutral.textBlack }}>
						Everything You Need to
						<span
							className='text-transparent bg-clip-text ml-3'
							style={{
								backgroundImage: gradients.electric,
							}}>
							Explore
						</span>
					</h2>
					<p
						className='text-xl leading-relaxed max-w-3xl mx-auto'
						style={{ color: colors.neutral.slateGray }}>
						From interactive maps to detailed reviews, <PERSON><PERSON><PERSON> provides all the tools you need for unforgettable discoveries.
					</p>
				</div>

				{/* Features Grid */}
				<div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8'>
					{/* Interactive Globe */}
					<div
						className='p-8 rounded-3xl backdrop-blur-md border hover:scale-105 transition-all duration-300'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
							boxShadow: shadows.glass,
						}}>
						<div
							className='w-16 h-16 mb-6 rounded-2xl flex items-center justify-center'
							style={{
								background: gradients.electric,
							}}>
							<FiGlobe className='w-8 h-8 text-white' />
						</div>
						<h3
							className='text-2xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Interactive Globe
						</h3>
						<p
							className='leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Explore the world in 3D. Zoom into neighborhoods, discover city rankings, and navigate with an intuitive globe interface.
						</p>
					</div>

					{/* Detailed Maps */}
					<div
						className='p-8 rounded-3xl backdrop-blur-md border hover:scale-105 transition-all duration-300'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
							boxShadow: shadows.glass,
						}}>
						<div
							className='w-16 h-16 mb-6 rounded-2xl flex items-center justify-center'
							style={{
								background: gradients.neon,
							}}>
							<FiMap className='w-8 h-8 text-white' />
						</div>
						<h3
							className='text-2xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Detailed Maps
						</h3>
						<p
							className='leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Switch between globe and flat map views. Get precise locations, directions, and explore points of interest with detailed information.
						</p>
					</div>

					{/* POI Profiles */}
					<div
						className='p-8 rounded-3xl backdrop-blur-md border hover:scale-105 transition-all duration-300'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
							boxShadow: shadows.glass,
						}}>
						<div
							className='w-16 h-16 mb-6 rounded-2xl flex items-center justify-center'
							style={{
								background: gradients.sunset,
							}}>
							<FiStar className='w-8 h-8 text-white' />
						</div>
						<h3
							className='text-2xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							POI Profiles
						</h3>
						<p
							className='leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Comprehensive profiles for every point of interest. Read reviews, view photos, check ratings, and get all the details you need.
						</p>
					</div>

					{/* Photo Gallery */}
					<div
						className='p-8 rounded-3xl backdrop-blur-md border hover:scale-105 transition-all duration-300'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
							boxShadow: shadows.glass,
						}}>
						<div
							className='w-16 h-16 mb-6 rounded-2xl flex items-center justify-center'
							style={{
								background: gradients.electric,
							}}>
							<FiCamera className='w-8 h-8 text-white' />
						</div>
						<h3
							className='text-2xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Rich Media
						</h3>
						<p
							className='leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Browse high-quality photos and media for every location. See what places really look like before you visit.
						</p>
					</div>

					{/* Community Reviews */}
					<div
						className='p-8 rounded-3xl backdrop-blur-md border hover:scale-105 transition-all duration-300'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
							boxShadow: shadows.glass,
						}}>
						<div
							className='w-16 h-16 mb-6 rounded-2xl flex items-center justify-center'
							style={{
								background: gradients.neon,
							}}>
							<FiUsers className='w-8 h-8 text-white' />
						</div>
						<h3
							className='text-2xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Community Insights
						</h3>
						<p
							className='leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Real reviews from real travelers. Get authentic insights and recommendations from people who've been there.
						</p>
					</div>

					{/* Smart Rankings */}
					<div
						className='p-8 rounded-3xl backdrop-blur-md border hover:scale-105 transition-all duration-300'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
							boxShadow: shadows.glass,
						}}>
						<div
							className='w-16 h-16 mb-6 rounded-2xl flex items-center justify-center'
							style={{
								background: gradients.sunset,
							}}>
							<FiTrendingUp className='w-8 h-8 text-white' />
						</div>
						<h3
							className='text-2xl font-bold mb-4'
							style={{ color: colors.neutral.textBlack }}>
							Smart Rankings
						</h3>
						<p
							className='leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Discover trending locations and top-rated spots. Our intelligent ranking system helps you find the best places to visit.
						</p>
					</div>
				</div>

				{/* Bottom CTA */}
				<div className='text-center mt-16'>
					<p
						className='text-lg mb-6'
						style={{ color: colors.neutral.slateGray }}>
						Ready to discover your next adventure?
					</p>
					<div
						className='inline-flex items-center space-x-2 px-6 py-3 rounded-full backdrop-blur-md border'
						style={{
							background: colors.glass.light,
							borderColor: colors.glass.medium,
						}}>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							🇹🇷 Turkey available now • More countries coming soon
						</span>
					</div>
				</div>
			</div>
		</div>
	);
};

export default FeaturesSection;
