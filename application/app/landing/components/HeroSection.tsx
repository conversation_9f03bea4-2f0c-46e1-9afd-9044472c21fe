/** @format */

import { colors, gradients, shadows } from '@/app/colors';
import React from 'react';
import {
	FiCompass,
	FiGlobe,
	FiMessageCircle,
	FiTrendingUp,
} from 'react-icons/fi';

interface HeroSectionProps {
	onGetStarted: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onGetStarted }) => {
	return (
		<div className='relative min-h-screen overflow-hidden'>
			{/* Revolutionary Cosmic Background */}
			<div
				className='absolute inset-0'
				style={{
					background: gradients.cosmic,
				}}>
				{/* Dynamic Aurora Orbs */}
				<div
					className='absolute top-10 left-10 w-96 h-96 rounded-full opacity-20 blur-3xl animate-pulse'
					style={{
						background: gradients.aurora,
					}}></div>
				<div
					className='absolute top-1/3 right-10 w-80 h-80 rounded-full opacity-15 blur-3xl animate-pulse'
					style={{
						background: gradients.innovationGradient,
						animationDelay: '2s',
					}}></div>
				<div
					className='absolute bottom-10 left-1/4 w-64 h-64 rounded-full opacity-25 blur-2xl animate-pulse'
					style={{
						background: gradients.luxuryGradient,
						animationDelay: '4s',
					}}></div>

				{/* Mystical Grid Pattern */}
				<div
					className='absolute inset-0 opacity-10'
					style={{
						backgroundImage: `
							radial-gradient(circle at 25% 25%, ${colors.psychological.innovation} 1px, transparent 1px),
							radial-gradient(circle at 75% 75%, ${colors.psychological.luxury} 1px, transparent 1px)
						`,
						backgroundSize: '60px 60px, 80px 80px',
					}}></div>

				{/* Floating Energy Particles */}
				<div
					className='absolute top-1/4 left-1/4 w-2 h-2 rounded-full animate-bounce'
					style={{
						backgroundColor: colors.psychological.energy,
						animationDuration: '3s',
					}}></div>
				<div
					className='absolute top-1/3 right-1/3 w-1 h-1 rounded-full animate-bounce'
					style={{
						backgroundColor: colors.psychological.innovation,
						animationDuration: '4s',
						animationDelay: '1s',
					}}></div>
				<div
					className='absolute bottom-1/3 left-1/2 w-3 h-3 rounded-full animate-bounce'
					style={{
						backgroundColor: colors.psychological.adventure,
						animationDuration: '5s',
						animationDelay: '2s',
					}}></div>
				<div
					className='absolute top-1/2 left-1/5 w-2 h-2 rounded-full animate-bounce'
					style={{
						backgroundColor: colors.psychological.luxury,
						animationDuration: '6s',
						animationDelay: '3s',
					}}></div>
			</div>

			<div className='relative max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-16'>
				<div className='text-center space-y-16'>
					{/* Mystical Badge */}
					<div className='animate-fade-in'>
						<div
							className='inline-flex items-center space-x-4 rounded-full px-8 py-4 backdrop-blur-md border-2'
							style={{
								background: colors.glass.light,
								borderColor: colors.psychological.innovation,
								boxShadow: `0 0 30px ${colors.psychological.energy}40`,
							}}>
							<FiGlobe
								className='w-6 h-6 animate-spin'
								style={{
									color: colors.psychological.innovation,
									animationDuration: '8s',
								}}
							/>
							<span
								className='text-base font-bold tracking-widest'
								style={{ color: colors.neutral.textBlack }}>
								UNLOCK THE WORLD
							</span>
						</div>
					</div>

					{/* Spectacular Headline */}
					<div className='animate-slide-up space-y-8'>
						<h1 className='text-7xl md:text-9xl font-black leading-none tracking-tight'>
							<div
								className='text-transparent bg-clip-text mb-4'
								style={{
									backgroundImage: gradients.trustGradient,
								}}>
								WIZLOP
							</div>
							<div className='text-4xl md:text-6xl font-light tracking-wide'>
								<span style={{ color: colors.psychological.mystery }}>
									Where
								</span>
								<span
									className='text-transparent bg-clip-text mx-4'
									style={{
										backgroundImage: gradients.adventureGradient,
									}}>
									Intelligence
								</span>
								<span style={{ color: colors.psychological.mystery }}>
									Meets
								</span>
								<br />
								<span
									className='text-transparent bg-clip-text'
									style={{
										backgroundImage: gradients.innovationGradient,
									}}>
									Exploration
								</span>
							</div>
						</h1>
					</div>

					{/* Revolutionary Mission Statement */}
					<div className='animate-slide-up animate-delay-100 max-w-6xl mx-auto'>
						<p
							className='text-2xl md:text-4xl font-light leading-relaxed mb-8'
							style={{ color: colors.psychological.trust }}>
							The world's most advanced location intelligence platform
						</p>
						<p
							className='text-lg md:text-xl leading-relaxed'
							style={{ color: colors.neutral.slateGray }}>
							Powered by multi-agent AI systems that understand geography,
							culture, and human behavior to unlock hidden gems across the globe
							through natural conversation.
						</p>
					</div>

					{/* Company Vision */}
					<div className='animate-slide-up animate-delay-150'>
						<div
							className='max-w-4xl mx-auto p-8 rounded-3xl backdrop-blur-md border'
							style={{
								background: colors.glass.light,
								borderColor: colors.glass.medium,
								boxShadow: shadows.glass,
							}}>
							<h2
								className='text-2xl md:text-3xl font-bold mb-4'
								style={{ color: colors.psychological.luxury }}>
								Our Mission
							</h2>
							<p
								className='text-lg leading-relaxed'
								style={{ color: colors.neutral.textBlack }}>
								To democratize global exploration by making every corner of the
								world accessible through intelligent conversation. We believe
								that discovery should be effortless, personal, and magical.
							</p>
						</div>
					</div>

					{/* Revolutionary Features */}
					<div className='animate-slide-up animate-delay-200 max-w-7xl mx-auto'>
						<div className='grid md:grid-cols-3 gap-8'>
							<div
								className='group text-center p-8 rounded-3xl backdrop-blur-md border-2 transition-all duration-500 hover:scale-105'
								style={{
									background: colors.glass.light,
									borderColor: colors.psychological.innovation,
									boxShadow: `0 0 20px ${colors.psychological.innovation}20`,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.boxShadow = `0 0 40px ${colors.psychological.innovation}40`;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.boxShadow = `0 0 20px ${colors.psychological.innovation}20`;
								}}>
								<div
									className='w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center group-hover:animate-pulse'
									style={{
										background: gradients.innovationGradient,
									}}>
									<FiMessageCircle className='w-10 h-10 text-white' />
								</div>
								<h3
									className='text-2xl font-bold mb-4'
									style={{ color: colors.psychological.innovation }}>
									Conversational AI
								</h3>
								<p
									className='leading-relaxed'
									style={{ color: colors.neutral.textBlack }}>
									Multi-agent AI systems that understand context, culture, and
									personal preferences through natural dialogue.
								</p>
							</div>

							<div
								className='group text-center p-8 rounded-3xl backdrop-blur-md border-2 transition-all duration-500 hover:scale-105'
								style={{
									background: colors.glass.light,
									borderColor: colors.psychological.adventure,
									boxShadow: `0 0 20px ${colors.psychological.adventure}20`,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.boxShadow = `0 0 40px ${colors.psychological.adventure}40`;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.boxShadow = `0 0 20px ${colors.psychological.adventure}20`;
								}}>
								<div
									className='w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center group-hover:animate-pulse'
									style={{
										background: gradients.adventureGradient,
									}}>
									<FiGlobe className='w-10 h-10 text-white' />
								</div>
								<h3
									className='text-2xl font-bold mb-4'
									style={{ color: colors.psychological.adventure }}>
									Global Intelligence
								</h3>
								<p
									className='leading-relaxed'
									style={{ color: colors.neutral.textBlack }}>
									Interactive 3D globe with real-time data, cultural insights,
									and neighborhood-level understanding across the world.
								</p>
							</div>

							<div
								className='group text-center p-8 rounded-3xl backdrop-blur-md border-2 transition-all duration-500 hover:scale-105'
								style={{
									background: colors.glass.light,
									borderColor: colors.psychological.luxury,
									boxShadow: `0 0 20px ${colors.psychological.luxury}20`,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.boxShadow = `0 0 40px ${colors.psychological.luxury}40`;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.boxShadow = `0 0 20px ${colors.psychological.luxury}20`;
								}}>
								<div
									className='w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center group-hover:animate-pulse'
									style={{
										background: gradients.luxuryGradient,
									}}>
									<FiCompass className='w-10 h-10 text-white' />
								</div>
								<h3
									className='text-2xl font-bold mb-4'
									style={{ color: colors.psychological.luxury }}>
									Intelligent Discovery
								</h3>
								<p
									className='leading-relaxed'
									style={{ color: colors.neutral.textBlack }}>
									Advanced algorithms analyze millions of data points to surface
									hidden gems and personalized recommendations.
								</p>
							</div>
						</div>
					</div>

					{/* Revolutionary CTA */}
					<div className='animate-slide-up animate-delay-300'>
						<div className='relative'>
							<button
								onClick={onGetStarted}
								className='group relative px-16 py-6 rounded-full text-2xl font-black text-white transition-all duration-700 hover:scale-110 active:scale-95'
								style={{
									background: gradients.aurora,
									boxShadow: `0 0 50px ${colors.psychological.energy}60`,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.boxShadow = `0 0 80px ${colors.psychological.energy}80`;
									e.currentTarget.style.transform =
										'scale(1.1) translateY(-5px)';
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.boxShadow = `0 0 50px ${colors.psychological.energy}60`;
									e.currentTarget.style.transform = 'scale(1)';
								}}>
								<span className='relative z-10 flex items-center space-x-4'>
									<span>ENTER THE FUTURE</span>
									<FiTrendingUp className='w-8 h-8 group-hover:translate-x-2 group-hover:rotate-12 transition-all duration-300' />
								</span>
								<div
									className='absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500'
									style={{
										background: gradients.cosmic,
									}}></div>
							</button>

							{/* Floating particles around button */}
							<div
								className='absolute -top-2 -left-2 w-4 h-4 rounded-full animate-bounce'
								style={{
									backgroundColor: colors.psychological.innovation,
									animationDelay: '0s',
								}}></div>
							<div
								className='absolute -top-2 -right-2 w-3 h-3 rounded-full animate-bounce'
								style={{
									backgroundColor: colors.psychological.adventure,
									animationDelay: '0.5s',
								}}></div>
							<div
								className='absolute -bottom-2 -left-2 w-2 h-2 rounded-full animate-bounce'
								style={{
									backgroundColor: colors.psychological.luxury,
									animationDelay: '1s',
								}}></div>
							<div
								className='absolute -bottom-2 -right-2 w-3 h-3 rounded-full animate-bounce'
								style={{
									backgroundColor: colors.psychological.energy,
									animationDelay: '1.5s',
								}}></div>
						</div>
					</div>

					{/* Example Query Card */}
					<div className='animate-slide-up animate-delay-300 max-w-2xl mx-auto'>
						<div
							className='backdrop-blur-md border rounded-3xl p-6'
							style={{
								background: colors.glass.light,
								borderColor: colors.glass.medium,
								boxShadow: shadows.glass,
							}}>
							<div className='flex items-center space-x-4'>
								<div
									className='w-12 h-12 rounded-full flex items-center justify-center'
									style={{
										background: gradients.electric,
									}}>
									<FiMessageCircle className='w-6 h-6 text-white' />
								</div>
								<div className='flex-1'>
									<p
										className='text-lg font-medium italic'
										style={{ color: colors.neutral.textBlack }}>
										"Show me hidden rooftop bars with amazing city views"
									</p>
									<p
										className='text-sm mt-1'
										style={{ color: colors.neutral.slateGray }}>
										Try asking naturally - our AI understands context and
										location
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default HeroSection;
