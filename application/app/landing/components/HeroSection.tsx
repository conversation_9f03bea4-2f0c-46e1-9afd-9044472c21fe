/** @format */

import { colors, gradients, shadows } from '@/app/colors';
import React from 'react';
import {
	FiCompass,
	FiGlobe,
	FiMessageCircle,
	FiTrendingUp,
} from 'react-icons/fi';
import './animations.css';

interface HeroSectionProps {
	onGetStarted: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onGetStarted }) => {
	return (
		<div
			className='relative min-h-screen overflow-hidden'
			role='main'
			aria-label='Hero section'>
			{/* Enhanced Cosmic Background with Depth */}
			<div
				className='absolute inset-0'
				style={{
					background: `radial-gradient(ellipse at top, ${colors.brand.navy}15 0%, ${colors.brand.navy} 100%), ${gradients.cosmic}`,
				}}>
				{/* Floating Geometric Elements */}
				<div className='absolute inset-0'>
					{/* Primary Aurora Orb with Enhanced Animation */}
					<div
						className='absolute top-20 left-20 w-96 h-96 rounded-full opacity-30 blur-3xl'
						style={{
							background: gradients.aurora,
							animation:
								'float 6s ease-in-out infinite, pulse 4s ease-in-out infinite',
						}}></div>

					{/* Secondary Innovation Orb */}
					<div
						className='absolute top-1/3 right-16 w-80 h-80 rounded-full opacity-20 blur-3xl'
						style={{
							background: gradients.innovationGradient,
							animation:
								'float 8s ease-in-out infinite reverse, pulse 6s ease-in-out infinite',
							animationDelay: '2s',
						}}></div>

					{/* Tertiary Luxury Orb */}
					<div
						className='absolute bottom-20 left-1/4 w-72 h-72 rounded-full opacity-25 blur-2xl'
						style={{
							background: gradients.luxuryGradient,
							animation:
								'float 10s ease-in-out infinite, pulse 5s ease-in-out infinite',
							animationDelay: '4s',
						}}></div>

					{/* Additional Floating Orbs for Depth */}
					<div
						className='absolute top-1/2 right-1/4 w-48 h-48 rounded-full opacity-15 blur-2xl'
						style={{
							background: gradients.adventureGradient,
							animation: 'float 7s ease-in-out infinite reverse',
							animationDelay: '1s',
						}}></div>
				</div>

				{/* Enhanced Mystical Grid Pattern */}
				<div
					className='absolute inset-0 opacity-15'
					style={{
						backgroundImage: `
							radial-gradient(circle at 25% 25%, ${colors.psychological.innovation} 1px, transparent 1px),
							radial-gradient(circle at 75% 75%, ${colors.psychological.luxury} 1px, transparent 1px),
							radial-gradient(circle at 50% 10%, ${colors.psychological.adventure} 0.5px, transparent 0.5px)
						`,
						backgroundSize: '60px 60px, 80px 80px, 40px 40px',
					}}></div>

				{/* Enhanced Floating Energy Particles */}
				<div className='absolute inset-0'>
					{/* Primary Energy Particles */}
					<div
						className='absolute top-1/4 left-1/4 w-3 h-3 rounded-full'
						style={{
							backgroundColor: colors.psychological.energy,
							animation:
								'bounce 3s ease-in-out infinite, glow 2s ease-in-out infinite alternate',
							boxShadow: `0 0 10px ${colors.psychological.energy}`,
						}}></div>
					<div
						className='absolute top-1/3 right-1/3 w-2 h-2 rounded-full'
						style={{
							backgroundColor: colors.psychological.innovation,
							animation:
								'bounce 4s ease-in-out infinite, glow 3s ease-in-out infinite alternate',
							animationDelay: '1s',
							boxShadow: `0 0 8px ${colors.psychological.innovation}`,
						}}></div>
					<div
						className='absolute bottom-1/3 left-1/2 w-4 h-4 rounded-full'
						style={{
							backgroundColor: colors.psychological.adventure,
							animation:
								'bounce 5s ease-in-out infinite, glow 2.5s ease-in-out infinite alternate',
							animationDelay: '2s',
							boxShadow: `0 0 12px ${colors.psychological.adventure}`,
						}}></div>
					<div
						className='absolute top-1/2 left-1/5 w-2 h-2 rounded-full'
						style={{
							backgroundColor: colors.psychological.luxury,
							animation:
								'bounce 6s ease-in-out infinite, glow 4s ease-in-out infinite alternate',
							animationDelay: '3s',
							boxShadow: `0 0 8px ${colors.psychological.luxury}`,
						}}></div>

					{/* Additional Micro Particles */}
					<div
						className='absolute top-3/4 right-1/5 w-1 h-1 rounded-full'
						style={{
							backgroundColor: colors.vibrant.electric,
							animation: 'bounce 7s ease-in-out infinite',
							animationDelay: '0.5s',
						}}></div>
					<div
						className='absolute top-1/5 right-1/2 w-1 h-1 rounded-full'
						style={{
							backgroundColor: colors.vibrant.neon,
							animation: 'bounce 8s ease-in-out infinite',
							animationDelay: '1.5s',
						}}></div>
				</div>
			</div>

			<div className='relative max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-16'>
				<div className='text-center space-y-16'>
					{/* Enhanced Mystical Badge */}
					<div className='animate-fade-in'>
						<div className='relative'>
							<div
								className='inline-flex items-center space-x-4 rounded-full px-10 py-5 backdrop-blur-md border-2 transition-all duration-500 hover:scale-105'
								style={{
									background: `linear-gradient(135deg, ${colors.glass.light}, ${colors.glass.blueGlass})`,
									borderColor: colors.psychological.innovation,
									boxShadow: `0 0 40px ${colors.psychological.energy}50, inset 0 1px 0 rgba(255,255,255,0.3)`,
								}}>
								<div className='relative'>
									<FiGlobe
										className='w-7 h-7'
										style={{
											color: colors.psychological.innovation,
											animation:
												'spin 12s linear infinite, pulse 3s ease-in-out infinite',
										}}
									/>
									<div
										className='absolute inset-0 w-7 h-7 rounded-full opacity-30'
										style={{
											background: colors.psychological.innovation,
											animation: 'ping 2s cubic-bezier(0, 0, 0.2, 1) infinite',
										}}></div>
								</div>
								<span
									className='text-lg font-black tracking-widest bg-clip-text text-transparent'
									style={{
										backgroundImage: `linear-gradient(135deg, ${colors.psychological.innovation}, ${colors.psychological.energy})`,
									}}>
									UNLOCK THE WORLD
								</span>
							</div>

							{/* Floating micro elements around badge */}
							<div
								className='absolute -top-1 -left-1 w-2 h-2 rounded-full'
								style={{
									backgroundColor: colors.psychological.adventure,
									animation: 'bounce 2s infinite',
								}}></div>
							<div
								className='absolute -top-1 -right-1 w-1 h-1 rounded-full'
								style={{
									backgroundColor: colors.psychological.luxury,
									animation: 'bounce 2.5s infinite',
									animationDelay: '0.5s',
								}}></div>
						</div>
					</div>

					{/* Enhanced Spectacular Headline */}
					<div className='animate-slide-up space-y-8'>
						<div className='relative'>
							<h1 className='text-5xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-black leading-none tracking-tight'>
								<div
									className='text-transparent bg-clip-text mb-6 relative'
									style={{
										backgroundImage: gradients.trustGradient,
										textShadow: `0 0 80px ${colors.psychological.energy}30`,
									}}>
									WIZLOP
									{/* Subtle glow effect behind text */}
									<div
										className='absolute inset-0 text-7xl md:text-9xl font-black opacity-20 blur-sm'
										style={{
											background: gradients.trustGradient,
											backgroundClip: 'text',
											WebkitBackgroundClip: 'text',
											color: 'transparent',
										}}>
										WIZLOP
									</div>
								</div>
								<div className='text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-light tracking-wide leading-tight'>
									<div className='mb-2'>
										<span
											className='transition-all duration-300 hover:scale-105 inline-block'
											style={{ color: colors.psychological.mystery }}>
											Where
										</span>
										<span
											className='text-transparent bg-clip-text mx-4 transition-all duration-300 hover:scale-105 inline-block'
											style={{
												backgroundImage: gradients.adventureGradient,
											}}>
											Intelligence
										</span>
										<span
											className='transition-all duration-300 hover:scale-105 inline-block'
											style={{ color: colors.psychological.mystery }}>
											Meets
										</span>
									</div>
									<div>
										<span
											className='text-transparent bg-clip-text transition-all duration-300 hover:scale-105 inline-block'
											style={{
												backgroundImage: gradients.innovationGradient,
											}}>
											Exploration
										</span>
									</div>
								</div>
							</h1>

							{/* Decorative elements around headline */}
							<div
								className='absolute -top-4 left-1/4 w-8 h-1 rounded-full opacity-60'
								style={{
									background: gradients.electric,
									animation: 'pulse 3s ease-in-out infinite',
								}}></div>
							<div
								className='absolute -bottom-4 right-1/4 w-6 h-1 rounded-full opacity-60'
								style={{
									background: gradients.adventureGradient,
									animation: 'pulse 4s ease-in-out infinite',
									animationDelay: '1s',
								}}></div>
						</div>
					</div>

					{/* Enhanced Revolutionary Mission Statement */}
					<div className='animate-slide-up animate-delay-100 max-w-6xl mx-auto'>
						<div className='relative'>
							<p
								className='text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-light leading-relaxed mb-8 transition-all duration-300 hover:scale-105'
								style={{
									color: colors.psychological.trust,
									textShadow: `0 0 20px ${colors.psychological.trust}20`,
								}}>
								The world's most advanced location intelligence platform
							</p>
							<div
								className='p-6 rounded-2xl backdrop-blur-sm border'
								style={{
									background: `linear-gradient(135deg, ${colors.glass.light}, ${colors.glass.blueGlass})`,
									borderColor: colors.glass.medium,
									boxShadow: `0 8px 32px ${colors.psychological.trust}10`,
								}}>
								<p
									className='text-lg md:text-xl leading-relaxed'
									style={{ color: colors.neutral.textBlack }}>
									Powered by{' '}
									<span
										className='font-semibold text-transparent bg-clip-text'
										style={{ backgroundImage: gradients.innovationGradient }}>
										multi-agent AI systems
									</span>{' '}
									that understand geography, culture, and human behavior to
									unlock{' '}
									<span
										className='font-semibold text-transparent bg-clip-text'
										style={{ backgroundImage: gradients.adventureGradient }}>
										hidden gems
									</span>{' '}
									across the globe through natural conversation.
								</p>
							</div>

							{/* Subtle accent lines */}
							<div
								className='absolute -left-4 top-1/2 w-1 h-16 rounded-full opacity-40'
								style={{
									background: gradients.electric,
								}}></div>
							<div
								className='absolute -right-4 top-1/2 w-1 h-12 rounded-full opacity-40'
								style={{
									background: gradients.adventureGradient,
								}}></div>
						</div>
					</div>

					{/* Enhanced Company Vision */}
					<div className='animate-slide-up animate-delay-150'>
						<div className='relative max-w-4xl mx-auto'>
							<div
								className='p-10 rounded-3xl backdrop-blur-md border-2 transition-all duration-500 hover:scale-105 group'
								style={{
									background: `linear-gradient(135deg, ${colors.glass.light}, ${colors.glass.greenGlass})`,
									borderColor: colors.psychological.luxury,
									boxShadow: `${shadows.glass}, 0 0 40px ${colors.psychological.luxury}20`,
								}}>
								<div className='flex items-start space-x-4'>
									<div
										className='w-16 h-16 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300'
										style={{
											background: gradients.luxuryGradient,
										}}>
										<FiCompass className='w-8 h-8 text-white' />
									</div>
									<div className='flex-1'>
										<h2
											className='text-2xl md:text-3xl font-bold mb-6 text-transparent bg-clip-text'
											style={{
												backgroundImage: `linear-gradient(135deg, ${colors.psychological.luxury}, ${colors.psychological.adventure})`,
											}}>
											Our Mission
										</h2>
										<p
											className='text-lg leading-relaxed'
											style={{ color: colors.neutral.textBlack }}>
											To{' '}
											<span
												className='font-semibold text-transparent bg-clip-text'
												style={{
													backgroundImage: gradients.adventureGradient,
												}}>
												democratize global exploration
											</span>{' '}
											by making every corner of the world accessible through
											intelligent conversation. We believe that discovery should
											be{' '}
											<span
												className='font-semibold text-transparent bg-clip-text'
												style={{
													backgroundImage: gradients.innovationGradient,
												}}>
												effortless, personal, and magical
											</span>
											.
										</p>
									</div>
								</div>

								{/* Decorative corner elements */}
								<div
									className='absolute top-4 right-4 w-3 h-3 rounded-full opacity-60'
									style={{
										background: colors.psychological.adventure,
										animation: 'pulse 2s ease-in-out infinite',
									}}></div>
								<div
									className='absolute bottom-4 left-4 w-2 h-2 rounded-full opacity-60'
									style={{
										background: colors.psychological.luxury,
										animation: 'pulse 3s ease-in-out infinite',
										animationDelay: '1s',
									}}></div>
							</div>
						</div>
					</div>

					{/* Revolutionary Features */}
					<div className='animate-slide-up animate-delay-200 max-w-7xl mx-auto'>
						<div className='grid md:grid-cols-3 gap-8'>
							<div
								className='group text-center p-8 rounded-3xl backdrop-blur-md border-2 transition-all duration-500 hover:scale-105'
								style={{
									background: colors.glass.light,
									borderColor: colors.psychological.innovation,
									boxShadow: `0 0 20px ${colors.psychological.innovation}20`,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.boxShadow = `0 0 40px ${colors.psychological.innovation}40`;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.boxShadow = `0 0 20px ${colors.psychological.innovation}20`;
								}}>
								<div
									className='w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center group-hover:animate-pulse'
									style={{
										background: gradients.innovationGradient,
									}}>
									<FiMessageCircle className='w-10 h-10 text-white' />
								</div>
								<h3
									className='text-2xl font-bold mb-4'
									style={{ color: colors.psychological.innovation }}>
									Conversational AI
								</h3>
								<p
									className='leading-relaxed'
									style={{ color: colors.neutral.textBlack }}>
									Multi-agent AI systems that understand context, culture, and
									personal preferences through natural dialogue.
								</p>
							</div>

							<div
								className='group text-center p-8 rounded-3xl backdrop-blur-md border-2 transition-all duration-500 hover:scale-105'
								style={{
									background: colors.glass.light,
									borderColor: colors.psychological.adventure,
									boxShadow: `0 0 20px ${colors.psychological.adventure}20`,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.boxShadow = `0 0 40px ${colors.psychological.adventure}40`;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.boxShadow = `0 0 20px ${colors.psychological.adventure}20`;
								}}>
								<div
									className='w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center group-hover:animate-pulse'
									style={{
										background: gradients.adventureGradient,
									}}>
									<FiGlobe className='w-10 h-10 text-white' />
								</div>
								<h3
									className='text-2xl font-bold mb-4'
									style={{ color: colors.psychological.adventure }}>
									Global Intelligence
								</h3>
								<p
									className='leading-relaxed'
									style={{ color: colors.neutral.textBlack }}>
									Interactive 3D globe with real-time data, cultural insights,
									and neighborhood-level understanding across the world.
								</p>
							</div>

							<div
								className='group text-center p-8 rounded-3xl backdrop-blur-md border-2 transition-all duration-500 hover:scale-105'
								style={{
									background: colors.glass.light,
									borderColor: colors.psychological.luxury,
									boxShadow: `0 0 20px ${colors.psychological.luxury}20`,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.boxShadow = `0 0 40px ${colors.psychological.luxury}40`;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.boxShadow = `0 0 20px ${colors.psychological.luxury}20`;
								}}>
								<div
									className='w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center group-hover:animate-pulse'
									style={{
										background: gradients.luxuryGradient,
									}}>
									<FiCompass className='w-10 h-10 text-white' />
								</div>
								<h3
									className='text-2xl font-bold mb-4'
									style={{ color: colors.psychological.luxury }}>
									Intelligent Discovery
								</h3>
								<p
									className='leading-relaxed'
									style={{ color: colors.neutral.textBlack }}>
									Advanced algorithms analyze millions of data points to surface
									hidden gems and personalized recommendations.
								</p>
							</div>
						</div>
					</div>

					{/* Enhanced Revolutionary CTA */}
					<div className='animate-slide-up animate-delay-300'>
						<div className='relative'>
							{/* Main CTA Button */}
							<button
								onClick={onGetStarted}
								className='group relative px-8 sm:px-12 md:px-16 lg:px-20 py-4 sm:py-6 md:py-8 rounded-full text-lg sm:text-xl md:text-2xl font-black text-white transition-all duration-700 hover:scale-110 active:scale-95 overflow-hidden touch-friendly'
								aria-label='Start exploring with Wizlop'
								role='button'
								tabIndex={0}
								style={{
									background: `linear-gradient(135deg, ${gradients.aurora}, ${gradients.cosmic})`,
									boxShadow: `0 0 60px ${colors.psychological.energy}60, inset 0 1px 0 rgba(255,255,255,0.3)`,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.boxShadow = `0 0 100px ${colors.psychological.energy}80, inset 0 1px 0 rgba(255,255,255,0.5)`;
									e.currentTarget.style.transform =
										'scale(1.1) translateY(-8px)';
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.boxShadow = `0 0 60px ${colors.psychological.energy}60, inset 0 1px 0 rgba(255,255,255,0.3)`;
									e.currentTarget.style.transform = 'scale(1)';
								}}>
								{/* Animated background overlay */}
								<div
									className='absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500'
									style={{
										background: `radial-gradient(circle at center, ${colors.psychological.innovation}40, ${colors.psychological.adventure}40)`,
									}}></div>

								{/* Button content */}
								<span className='relative z-10 flex items-center space-x-4'>
									<span className='tracking-wider'>START EXPLORING</span>
									<div className='relative'>
										<FiTrendingUp className='w-8 h-8 group-hover:translate-x-3 group-hover:rotate-12 transition-all duration-300' />
										<div
											className='absolute inset-0 w-8 h-8 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300'
											style={{
												background: colors.psychological.energy,
												filter: 'blur(8px)',
											}}></div>
									</div>
								</span>

								{/* Ripple effect */}
								<div
									className='absolute inset-0 rounded-full opacity-0 group-hover:opacity-30 transition-opacity duration-700'
									style={{
										background: `radial-gradient(circle, ${colors.psychological.energy} 0%, transparent 70%)`,
										animation: 'ping 1s cubic-bezier(0, 0, 0.2, 1) infinite',
									}}></div>
							</button>

							{/* Enhanced Floating particles around button */}
							<div className='absolute inset-0 pointer-events-none'>
								{/* Primary particles */}
								<div
									className='absolute -top-4 -left-4 w-5 h-5 rounded-full'
									style={{
										backgroundColor: colors.psychological.innovation,
										animation:
											'bounce 2s infinite, glow 3s ease-in-out infinite alternate',
										boxShadow: `0 0 15px ${colors.psychological.innovation}`,
									}}></div>
								<div
									className='absolute -top-4 -right-4 w-4 h-4 rounded-full'
									style={{
										backgroundColor: colors.psychological.adventure,
										animation:
											'bounce 2.5s infinite, glow 2s ease-in-out infinite alternate',
										animationDelay: '0.5s',
										boxShadow: `0 0 12px ${colors.psychological.adventure}`,
									}}></div>
								<div
									className='absolute -bottom-4 -left-4 w-3 h-3 rounded-full'
									style={{
										backgroundColor: colors.psychological.luxury,
										animation:
											'bounce 3s infinite, glow 4s ease-in-out infinite alternate',
										animationDelay: '1s',
										boxShadow: `0 0 10px ${colors.psychological.luxury}`,
									}}></div>
								<div
									className='absolute -bottom-4 -right-4 w-4 h-4 rounded-full'
									style={{
										backgroundColor: colors.psychological.energy,
										animation:
											'bounce 2.2s infinite, glow 2.5s ease-in-out infinite alternate',
										animationDelay: '1.5s',
										boxShadow: `0 0 12px ${colors.psychological.energy}`,
									}}></div>

								{/* Secondary micro particles */}
								<div
									className='absolute top-1/2 -left-8 w-2 h-2 rounded-full'
									style={{
										backgroundColor: colors.vibrant.electric,
										animation: 'bounce 4s infinite',
										animationDelay: '0.3s',
									}}></div>
								<div
									className='absolute top-1/2 -right-8 w-1 h-1 rounded-full'
									style={{
										backgroundColor: colors.vibrant.neon,
										animation: 'bounce 3.5s infinite',
										animationDelay: '0.8s',
									}}></div>
							</div>
						</div>
					</div>

					{/* Enhanced Example Query Card */}
					<div className='animate-slide-up animate-delay-400 max-w-3xl mx-auto'>
						<div className='relative'>
							<div
								className='backdrop-blur-md border-2 rounded-3xl p-8 transition-all duration-500 hover:scale-105 group'
								style={{
									background: `linear-gradient(135deg, ${colors.glass.light}, ${colors.glass.blueGlass})`,
									borderColor: colors.psychological.innovation,
									boxShadow: `${shadows.glass}, 0 0 30px ${colors.psychological.innovation}20`,
								}}>
								<div className='flex items-start space-x-6'>
									<div
										className='w-16 h-16 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300'
										style={{
											background: gradients.electric,
										}}>
										<FiMessageCircle className='w-8 h-8 text-white' />
									</div>
									<div className='flex-1'>
										<p
											className='text-xl font-medium italic mb-3 text-transparent bg-clip-text'
											style={{
												backgroundImage: `linear-gradient(135deg, ${colors.neutral.textBlack}, ${colors.psychological.innovation})`,
											}}>
											"Show me hidden rooftop bars with amazing city views"
										</p>
										<p
											className='text-base leading-relaxed'
											style={{ color: colors.neutral.slateGray }}>
											Try asking naturally - our AI understands context,
											location preferences, and cultural nuances to deliver
											personalized recommendations.
										</p>

										{/* Example tags */}
										<div className='flex flex-wrap gap-2 mt-4'>
											{[
												'Natural Language',
												'Context Aware',
												'Cultural Intelligence',
											].map((tag, index) => (
												<span
													key={index}
													className='px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm border'
													style={{
														background: colors.glass.light,
														borderColor: colors.glass.medium,
														color: colors.psychological.innovation,
													}}>
													{tag}
												</span>
											))}
										</div>
									</div>
								</div>

								{/* Decorative corner accent */}
								<div
									className='absolute top-4 right-4 w-3 h-3 rounded-full opacity-60'
									style={{
										background: colors.psychological.adventure,
										animation: 'pulse 2s ease-in-out infinite',
									}}></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default HeroSection;
