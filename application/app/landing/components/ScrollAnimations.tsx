/** @format */

import React, { useEffect, useRef, useState } from 'react';
import { colors, gradients } from '@/app/colors';
import './animations.css';

interface ScrollAnimationProps {
  children: React.ReactNode;
  animationType?: 'fade-up' | 'fade-in' | 'slide-left' | 'slide-right' | 'scale-up' | 'rotate-in';
  delay?: number;
  threshold?: number;
  className?: string;
}

const ScrollAnimation: React.FC<ScrollAnimationProps> = ({
  children,
  animationType = 'fade-up',
  delay = 0,
  threshold = 0.1,
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            setIsVisible(true);
          }, delay);
        }
      },
      { threshold }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => {
      if (elementRef.current) {
        observer.unobserve(elementRef.current);
      }
    };
  }, [delay, threshold]);

  const getAnimationClass = () => {
    const baseClass = 'transition-all duration-1000 ease-out';
    
    if (!isVisible) {
      switch (animationType) {
        case 'fade-up':
          return `${baseClass} opacity-0 translate-y-12`;
        case 'fade-in':
          return `${baseClass} opacity-0`;
        case 'slide-left':
          return `${baseClass} opacity-0 -translate-x-12`;
        case 'slide-right':
          return `${baseClass} opacity-0 translate-x-12`;
        case 'scale-up':
          return `${baseClass} opacity-0 scale-95`;
        case 'rotate-in':
          return `${baseClass} opacity-0 rotate-12 scale-95`;
        default:
          return `${baseClass} opacity-0 translate-y-12`;
      }
    }
    
    return `${baseClass} opacity-100 translate-y-0 translate-x-0 scale-100 rotate-0`;
  };

  return (
    <div ref={elementRef} className={`${getAnimationClass()} ${className}`}>
      {children}
    </div>
  );
};

// Parallax component for background elements
interface ParallaxProps {
  children: React.ReactNode;
  speed?: number;
  className?: string;
}

const Parallax: React.FC<ParallaxProps> = ({ children, speed = 0.5, className = '' }) => {
  const [offsetY, setOffsetY] = useState(0);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (elementRef.current) {
        const rect = elementRef.current.getBoundingClientRect();
        const scrolled = window.pageYOffset;
        const rate = scrolled * -speed;
        setOffsetY(rate);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return (
    <div
      ref={elementRef}
      className={`parallax-element ${className}`}
      style={{
        transform: `translateY(${offsetY}px)`,
      }}>
      {children}
    </div>
  );
};

// Magnetic hover effect component
interface MagneticProps {
  children: React.ReactNode;
  strength?: number;
  className?: string;
}

const Magnetic: React.FC<MagneticProps> = ({ children, strength = 0.3, className = '' }) => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const elementRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (elementRef.current) {
      const rect = elementRef.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const deltaX = (e.clientX - centerX) * strength;
      const deltaY = (e.clientY - centerY) * strength;
      
      setPosition({ x: deltaX, y: deltaY });
    }
  };

  const handleMouseLeave = () => {
    setPosition({ x: 0, y: 0 });
  };

  return (
    <div
      ref={elementRef}
      className={`magnetic transition-transform duration-300 ease-out ${className}`}
      style={{
        transform: `translate(${position.x}px, ${position.y}px)`,
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}>
      {children}
    </div>
  );
};

// Floating elements component
interface FloatingElementsProps {
  count?: number;
  className?: string;
}

const FloatingElements: React.FC<FloatingElementsProps> = ({ count = 6, className = '' }) => {
  const elements = Array.from({ length: count }, (_, i) => ({
    id: i,
    size: Math.random() * 8 + 4,
    left: Math.random() * 100,
    animationDelay: Math.random() * 5,
    animationDuration: Math.random() * 10 + 10,
    color: [
      colors.psychological.innovation,
      colors.psychological.adventure,
      colors.psychological.luxury,
      colors.psychological.energy,
    ][Math.floor(Math.random() * 4)],
  }));

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {elements.map((element) => (
        <div
          key={element.id}
          className="absolute rounded-full opacity-20"
          style={{
            width: `${element.size}px`,
            height: `${element.size}px`,
            left: `${element.left}%`,
            backgroundColor: element.color,
            animation: `float ${element.animationDuration}s ease-in-out infinite`,
            animationDelay: `${element.animationDelay}s`,
          }}></div>
      ))}
    </div>
  );
};

// Typewriter effect component
interface TypewriterProps {
  text: string;
  speed?: number;
  className?: string;
  onComplete?: () => void;
}

const Typewriter: React.FC<TypewriterProps> = ({ 
  text, 
  speed = 100, 
  className = '',
  onComplete 
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timeout);
    } else if (onComplete) {
      onComplete();
    }
  }, [currentIndex, text, speed, onComplete]);

  return (
    <span className={`typewriter ${className}`}>
      {displayText}
      <span className="animate-pulse">|</span>
    </span>
  );
};

// Morphing shape component
interface MorphingShapeProps {
  className?: string;
  color?: string;
}

const MorphingShape: React.FC<MorphingShapeProps> = ({ 
  className = '', 
  color = colors.psychological.innovation 
}) => {
  return (
    <div
      className={`morph-hover transition-all duration-1000 ${className}`}
      style={{
        background: `linear-gradient(45deg, ${color}40, ${color}20)`,
        borderRadius: '50px',
        width: '100px',
        height: '100px',
      }}></div>
  );
};

export {
  ScrollAnimation,
  Parallax,
  Magnetic,
  FloatingElements,
  Typewriter,
  MorphingShape,
};
