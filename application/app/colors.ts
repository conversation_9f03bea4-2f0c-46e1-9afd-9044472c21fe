/** @format */

// Wizlop Color System - Centralized color palette
// Based on Logo 5 gradient colors for consistent branding
// Enhanced for modern glassmorphism and vibrant UI design

export const colors = {
	// 1. Primary Brand Colors
	brand: {
		blue: '#33C2FF', // Bright Cyan Blue - Main brand anchor (buttons, nav, footer)
		navy: '#01034F', // Deep Navy - Secondary brand tone (hover, text, headers)
		green: '#80ED99', // Light Green - Nature-focused accent (tabs, highlights)
		darkBlue: '#1565C0', // Dark blue for additional UI elements
		teal: '#26A69A', // Teal for additional UI elements
	},

	// 2. Supporting / Gradient Colors
	supporting: {
		lightBlue: '#66D0FF', // Light Blue - Background accents, cards, hover states
		softNavy: '#1A1F5C', // Soft Navy - Subtle backgrounds, borders
		mintGreen: '#A3F7B5', // Mint Green - Success states, positive accents
	},

	// 3. Black & White / Neutrals
	neutral: {
		textBlack: '#1B1B1E', // Text Black - Primary text, logo variant
		slateGray: '#5E6E7E', // Slate Gray - Secondary text, inputs
		cloudWhite: '#F5F7F8', // Cloud White - Background
		lightMistGray: '#E6F2F0', // Light Mist Gray - Card background, hover fill
		pureWhite: '#FFFFFF', // Pure white for high contrast
		charcoal: '#2C2C2E', // Dark charcoal for dark mode
	},

	// 4. Glassmorphism Colors
	glass: {
		// Glass backgrounds with opacity
		light: 'rgba(255, 255, 255, 0.25)',
		medium: 'rgba(255, 255, 255, 0.15)',
		dark: 'rgba(255, 255, 255, 0.1)',
		// Colored glass variants
		blueGlass: 'rgba(51, 194, 255, 0.15)',
		greenGlass: 'rgba(128, 237, 153, 0.15)',
		navyGlass: 'rgba(1, 3, 79, 0.25)',
		// Backdrop blur support
		backdrop: 'rgba(255, 255, 255, 0.8)',
		backdropDark: 'rgba(27, 27, 30, 0.8)',
	},

	// 5. Vibrant Accent Colors (Enhanced for psychological impact)
	vibrant: {
		electric: '#00D4FF', // Electric blue for highlights and energy
		neon: '#39FF14', // Neon green for success and growth
		coral: '#FF6B6B', // Coral for warmth and attention
		purple: '#8B5CF6', // Purple for premium and mystery
		gold: '#FFD700', // Gold for achievement and value
		sunset: '#FF8A65', // Sunset orange for comfort and warmth
		magenta: '#FF1493', // Deep pink for creativity and passion
		cyan: '#00FFFF', // Bright cyan for clarity and freshness
		lime: '#32CD32', // Lime green for vitality and nature
		amber: '#FFBF00', // Amber for wisdom and energy
	},

	// 6. Psychological Color Mapping (Following info.txt principles)
	psychological: {
		trust: '#33C2FF', // Primary blue - builds trust and reliability
		energy: '#00D4FF', // Electric blue - creates excitement and action
		growth: '#80ED99', // Brand green - represents growth and harmony
		luxury: '#8B5CF6', // Purple - premium feel and sophistication
		warmth: '#FF8A65', // Sunset orange - comfort and approachability
		success: '#39FF14', // Neon green - achievement and positive outcomes
		attention: '#FF6B6B', // Coral - draws focus without being alarming
		mystery: '#01034F', // Deep navy - creates intrigue and depth
		innovation: '#00FFFF', // Cyan - represents cutting-edge technology
		adventure: '#FFD700', // Gold - excitement and discovery
	},

	// 7. Utility Colors
	utility: {
		success: '#4CAF50',
		warning: '#FFB74D',
		error: '#E57373',
		errorLight: '#FFCDD2',
		info: '#29B6F6',
	},

	// 8. Extended Palette for UI Components
	ui: {
		// Light variations for backgrounds
		blue50: '#F0FAFF', // Very light cyan blue
		blue100: '#E1F5FF', // Light cyan blue
		blue200: '#B3E5FF', // Medium light cyan blue

		navy50: '#F8F8FB', // Very light navy
		navy100: '#F1F1F7', // Light navy
		navy200: '#E3E3ED', // Medium light navy

		green50: '#F5FDF8', // Very light green
		green100: '#EBFBF0', // Light green
		green200: '#D7F7E1', // Medium light green

		// Gray scale
		gray50: '#F9FAFB',
		gray100: '#F3F4F6',
		gray200: '#E5E7EB',
		gray300: '#D1D5DB',
		gray400: '#9CA3AF',
		gray500: '#6B7280',
		gray600: '#4B5563',
		gray700: '#374151',
		gray800: '#1F2937',
		gray900: '#111827',
	},
} as const;

// Tailwind CSS custom color classes
export const tailwindColors = {
	// Primary brand colors
	'wizlop-blue': colors.brand.blue,
	'wizlop-navy': colors.brand.navy,
	'wizlop-green': colors.brand.green,

	// Supporting colors
	'wizlop-light-blue': colors.supporting.lightBlue,
	'wizlop-soft-navy': colors.supporting.softNavy,
	'wizlop-mint-green': colors.supporting.mintGreen,

	// Neutrals
	'wizlop-text': colors.neutral.textBlack,
	'wizlop-slate': colors.neutral.slateGray,
	'wizlop-cloud': colors.neutral.cloudWhite,
	'wizlop-mist': colors.neutral.lightMistGray,
} as const;

// Enhanced Gradient combinations for modern UI
export const gradients = {
	// Primary brand gradients
	primary: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
	secondary: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
	soft: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.supporting.mintGreen} 100%)`,
	background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,

	// Glassmorphism gradients
	glass: `linear-gradient(135deg, ${colors.glass.light} 0%, ${colors.glass.medium} 100%)`,
	glassBlue: `linear-gradient(135deg, ${colors.glass.blueGlass} 0%, ${colors.glass.light} 100%)`,
	glassGreen: `linear-gradient(135deg, ${colors.glass.greenGlass} 0%, ${colors.glass.light} 100%)`,

	// Vibrant gradients for modern appeal
	electric: `linear-gradient(135deg, ${colors.vibrant.electric} 0%, ${colors.brand.blue} 100%)`,
	sunset: `linear-gradient(135deg, ${colors.vibrant.sunset} 0%, ${colors.vibrant.coral} 100%)`,
	neon: `linear-gradient(135deg, ${colors.vibrant.neon} 0%, ${colors.brand.green} 100%)`,

	// Subtle background gradients
	lightMesh: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 50%, ${colors.ui.blue50} 100%)`,
	darkMesh: `linear-gradient(135deg, ${colors.supporting.softNavy} 0%, ${colors.brand.navy} 100%)`,

	// Psychological gradients for enhanced user engagement
	trustGradient: `linear-gradient(135deg, ${colors.psychological.trust} 0%, ${colors.psychological.energy} 100%)`,
	luxuryGradient: `linear-gradient(135deg, ${colors.psychological.luxury} 0%, ${colors.psychological.mystery} 100%)`,
	adventureGradient: `linear-gradient(135deg, ${colors.psychological.adventure} 0%, ${colors.psychological.warmth} 100%)`,
	innovationGradient: `linear-gradient(135deg, ${colors.psychological.innovation} 0%, ${colors.psychological.growth} 100%)`,

	// Multi-stop gradients for complex backgrounds
	aurora: `linear-gradient(135deg, ${colors.vibrant.purple} 0%, ${colors.vibrant.electric} 25%, ${colors.vibrant.cyan} 50%, ${colors.vibrant.neon} 75%, ${colors.vibrant.magenta} 100%)`,
	cosmic: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.vibrant.purple} 30%, ${colors.vibrant.electric} 70%, ${colors.brand.blue} 100%)`,
	nature: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.vibrant.lime} 50%, ${colors.vibrant.neon} 100%)`,
	sunsetGlow: `linear-gradient(135deg, ${colors.vibrant.gold} 0%, ${colors.vibrant.sunset} 50%, ${colors.vibrant.coral} 100%)`,
} as const;

// Shadow system for depth and glassmorphism
export const shadows = {
	// Standard shadows
	sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
	md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
	lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
	xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',

	// Glassmorphism shadows
	glass: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
	glassInset: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.5)',

	// Colored shadows for vibrant elements
	blue: `0 8px 25px -8px ${colors.brand.blue}40`,
	green: `0 8px 25px -8px ${colors.brand.green}40`,
	electric: `0 8px 25px -8px ${colors.vibrant.electric}40`,

	// Glow effects
	glow: `0 0 20px ${colors.brand.blue}30`,
	glowGreen: `0 0 20px ${colors.brand.green}30`,
	glowElectric: `0 0 20px ${colors.vibrant.electric}30`,
} as const;

// POI Category Colors - Centralized mapping using brand colors
export const poiCategoryColors = {
	// Food & Dining
	restaurant: colors.vibrant.coral,
	cafe: colors.vibrant.sunset,
	bar: colors.brand.blue,

	// Shopping
	shop: colors.brand.teal,
	supermarket: colors.supporting.lightBlue,
	market: colors.brand.green,

	// Services
	bank: colors.vibrant.gold,
	hospital: colors.utility.error,
	pharmacy: colors.utility.success,
	post_office: colors.brand.navy,

	// Transportation
	parking: colors.neutral.slateGray,
	gas_station: colors.vibrant.sunset,
	bus_station: colors.brand.darkBlue,
	transport: colors.brand.darkBlue,

	// Recreation & Tourism
	park: colors.brand.green,
	recreation: colors.supporting.mintGreen,
	tourism: colors.vibrant.purple,
	attraction: colors.vibrant.electric,

	// Education & Culture
	school: colors.vibrant.gold,
	university: colors.brand.navy,
	library: colors.supporting.softNavy,
	place_of_worship: colors.vibrant.purple,

	// Accommodation
	hotel: colors.brand.blue,
	accommodation: colors.supporting.lightBlue,

	// Default fallback
	default: colors.neutral.slateGray,
} as const;

// Animation and transition utilities
export const animations = {
	// Timing functions
	easing: {
		smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
		bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
		elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
	},

	// Duration presets
	duration: {
		fast: '150ms',
		normal: '300ms',
		slow: '500ms',
		slower: '750ms',
	},

	// Common transition combinations
	transition: {
		all: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
		colors:
			'color 300ms ease, background-color 300ms ease, border-color 300ms ease',
		transform: 'transform 300ms cubic-bezier(0.4, 0, 0.2, 1)',
		opacity: 'opacity 300ms ease',
		shadow: 'box-shadow 300ms ease',
	},
} as const;

// Common color utilities
export const getColorWithOpacity = (color: string, opacity: number): string => {
	return `${color}${Math.round(opacity * 255)
		.toString(16)
		.padStart(2, '0')}`;
};

// Utility to get POI color by category/subcategory
export const getPOIColor = (category: string, subcategory?: string): string => {
	const key = subcategory || category;
	return (
		poiCategoryColors[key as keyof typeof poiCategoryColors] ||
		poiCategoryColors.default
	);
};

// Utility to create glassmorphism styles
export const createGlassStyle = (
	opacity: 'light' | 'medium' | 'dark' = 'medium'
) => ({
	background: colors.glass[opacity],
	backdropFilter: 'blur(10px)',
	border: `1px solid ${colors.glass.light}`,
	boxShadow: shadows.glass,
});

export default colors;
