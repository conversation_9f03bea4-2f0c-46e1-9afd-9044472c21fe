/** @format */

import { CreditsDisplay } from '@/app/shared/credits';
import { zIndexLayers } from 'app/chat/styles';
import { TopBarProps } from 'app/chat/types';
import { colors } from 'app/colors';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useRef, useState } from 'react';
import { FaBars, FaGlobe, FaMap } from 'react-icons/fa';
import UserDropdown from './UserDropdown';

const TopBar: React.FC<TopBarProps> = ({
	isLeftOpen,
	isRightOpen,
	setIsLeftOpen,
	setIsRightOpen,
	startNewChat,
	userLocation,
	locationError,
	locationLoading,
	requestAutoLocation,
}) => {
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const { data: session } = useSession();
	const router = useRouter();
	const buttonRef = useRef<HTMLDivElement>(null);

	const getUserInitials = () => {
		if (session?.user?.username) {
			return session.user.username.substring(0, 2).toUpperCase();
		}
		if (session?.user?.email) {
			return session.user.email.substring(0, 2).toUpperCase();
		}
		return 'U';
	};

	return (
		<div
			className={`h-[60px] px-4 flex items-center justify-between border-b relative backdrop-blur-md ${zIndexLayers.topBar}`}
			style={{
				background: colors.glass.backdrop,
				borderColor: colors.glass.medium,
				boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
			}}>
			<div className='flex items-center gap-3'>
				{!isLeftOpen && (
					<button
						onClick={() => setIsLeftOpen(true)}
						className='p-2 rounded-lg transition-all duration-300 backdrop-blur-md border'
						style={{
							color: colors.neutral.slateGray,
							background: colors.glass.light,
							borderColor: colors.glass.medium,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.background = colors.glass.medium;
							e.currentTarget.style.transform = 'translateY(-1px)';
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.background = colors.glass.light;
							e.currentTarget.style.transform = 'translateY(0)';
						}}>
						<FaBars />
					</button>
				)}

				<button
					onClick={startNewChat}
					className='px-4 py-2 text-sm rounded-xl font-medium transition-all duration-300 backdrop-blur-md border'
					style={{
						background: colors.glass.blueGlass,
						color: colors.brand.blue,
						borderColor: colors.glass.medium,
					}}
					onMouseEnter={(e) => {
						e.currentTarget.style.background = colors.glass.light;
						e.currentTarget.style.transform = 'translateY(-1px)';
					}}
					onMouseLeave={(e) => {
						e.currentTarget.style.background = colors.glass.blueGlass;
						e.currentTarget.style.transform = 'translateY(0)';
					}}>
					New Chat
				</button>
			</div>

			<div className='flex-1 flex justify-center'>
				<div className='flex items-center'>
					<div className='w-10 h-10 relative'>
						<Image
							src='/logo/512x512.png'
							alt='Logo'
							width={40}
							height={40}
							className='rounded-lg'
						/>
					</div>
				</div>
			</div>

			<div className='flex items-center gap-3'>
				{/* Credits Display */}
				<CreditsDisplay
					size='small'
					showAddButton={false}
				/>

				<button
					onClick={() => router.push('/globe')}
					className='p-2 rounded-lg transition-all duration-300 backdrop-blur-md border'
					style={{
						color: colors.brand.blue,
						background: colors.glass.blueGlass,
						borderColor: colors.glass.medium,
					}}
					onMouseEnter={(e) => {
						e.currentTarget.style.background = colors.glass.light;
						e.currentTarget.style.transform = 'translateY(-1px)';
					}}
					onMouseLeave={(e) => {
						e.currentTarget.style.background = colors.glass.blueGlass;
						e.currentTarget.style.transform = 'translateY(0)';
					}}
					title='Interactive Globe'>
					<FaGlobe />
				</button>

				{!isRightOpen && (
					<button
						onClick={() => setIsRightOpen(true)}
						className='p-2 rounded-lg transition-all duration-300 backdrop-blur-md border'
						style={{
							color: colors.brand.green,
							background: colors.glass.greenGlass,
							borderColor: colors.glass.medium,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.background = colors.glass.light;
							e.currentTarget.style.transform = 'translateY(-1px)';
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.background = colors.glass.greenGlass;
							e.currentTarget.style.transform = 'translateY(0)';
						}}>
						<FaMap />
					</button>
				)}

				{/* User Dropdown */}
				<div className='relative'>
					<div
						ref={buttonRef}
						onClick={() => setDropdownOpen((prev) => !prev)}
						className='w-10 h-10 bg-gradient-to-br from-wizlop-500 to-wizlop-600 text-white rounded-xl flex items-center justify-center cursor-pointer select-none hover:from-wizlop-600 hover:to-wizlop-700 transition-all duration-300 shadow-lg'>
						{getUserInitials()}
					</div>

					<UserDropdown
						isOpen={dropdownOpen}
						onClose={() => setDropdownOpen(false)}
						buttonRef={buttonRef}
						userLocation={userLocation}
						locationError={locationError}
						locationLoading={locationLoading}
						requestAutoLocation={requestAutoLocation}
					/>
				</div>
			</div>
		</div>
	);
};

export default TopBar;
