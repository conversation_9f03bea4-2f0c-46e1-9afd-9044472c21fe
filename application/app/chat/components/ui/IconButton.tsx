/** @format */

import { colors } from 'app/colors';
import React from 'react';

interface IconButtonProps {
	onClick: () => void;
	icon: React.ReactNode;
	title?: string;
	variant?: 'default' | 'ghost' | 'danger';
	size?: 'sm' | 'md' | 'lg';
	disabled?: boolean;
	className?: string;
}

const IconButton: React.FC<IconButtonProps> = ({
	onClick,
	icon,
	title,
	variant = 'default',
	size = 'md',
	disabled = false,
	className = '',
}) => {
	const getVariantStyles = () => {
		switch (variant) {
			case 'ghost':
				return {
					color: colors.ui.gray400,
					hoverColor: colors.supporting.lightBlue,
					backgroundColor: 'transparent',
				};
			case 'danger':
				return {
					color: colors.utility.error,
					hoverColor: colors.vibrant.coral,
					backgroundColor: 'transparent',
				};
			default:
				return {
					color: colors.neutral.slateGray,
					hoverColor: colors.brand.green,
					backgroundColor: 'transparent',
					hoverBg: colors.ui.green100,
				};
		}
	};

	const getSizeStyles = () => {
		switch (size) {
			case 'sm':
				return 'p-1';
			case 'md':
				return 'p-2';
			case 'lg':
				return 'p-3';
			default:
				return 'p-2';
		}
	};

	const variantStyles = getVariantStyles();
	const sizeClasses = getSizeStyles();

	return (
		<button
			onClick={onClick}
			disabled={disabled}
			title={title}
			className={`${sizeClasses} rounded-lg transition-colors ${className}`}
			style={{
				color: disabled ? colors.ui.gray300 : variantStyles.color,
				backgroundColor: variantStyles.backgroundColor,
			}}
			onMouseEnter={(e) => {
				if (!disabled) {
					e.currentTarget.style.color = variantStyles.hoverColor;
					if (variantStyles.hoverBg) {
						e.currentTarget.style.backgroundColor = variantStyles.hoverBg;
					}
				}
			}}
			onMouseLeave={(e) => {
				if (!disabled) {
					e.currentTarget.style.color = variantStyles.color;
					e.currentTarget.style.backgroundColor = variantStyles.backgroundColor;
				}
			}}>
			{icon}
		</button>
	);
};

export default IconButton;
