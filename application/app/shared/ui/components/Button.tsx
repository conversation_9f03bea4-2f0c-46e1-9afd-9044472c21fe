/** @format */

import Link from 'next/link';
import { ButtonHTMLAttributes, ReactNode } from 'react';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
	variant?: 'primary' | 'secondary' | 'outline';
	size?: 'sm' | 'md' | 'lg';
	children: ReactNode;
	href?: string;
	loading?: boolean;
}

export function Button({
	variant = 'primary',
	size = 'md',
	children,
	href,
	loading = false,
	className = '',
	disabled,
	...props
}: ButtonProps) {
	const baseClasses =
		'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

	const variants = {
		primary:
			'bg-wizlop-blue text-white hover:bg-wizlop-light-blue focus:ring-wizlop-blue',
		secondary:
			'bg-wizlop-slate text-white hover:bg-wizlop-navy focus:ring-wizlop-slate',
		outline:
			'bg-white text-wizlop-blue border-2 border-wizlop-blue hover:bg-wizlop-cloud focus:ring-wizlop-blue',
	};

	const sizes = {
		sm: 'px-4 py-2 text-sm',
		md: 'px-6 py-3 text-base',
		lg: 'px-8 py-4 text-lg',
	};

	const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;

	const content = (
		<>
			{loading && (
				<div className='w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2' />
			)}
			{children}
		</>
	);

	if (href) {
		return (
			<Link
				href={href}
				className={classes}>
				{content}
			</Link>
		);
	}

	return (
		<button
			className={classes}
			disabled={disabled || loading}
			{...props}>
			{content}
		</button>
	);
}
