/** @format */

'use client';

import { getPOIColor as getPOIColorFromSystem } from '@/app/colors';
import L from 'leaflet';
import React from 'react';
import { Marker, Popup } from 'react-leaflet';

interface POI {
	poi_type: 'official' | 'user_temp' | 'user_approved';
	poi_id?: number;
	temp_id?: number;
	approved_id?: number;
	name: string;
	category: string;
	subcategory?: string;
	latitude: number;
	longitude: number;
	city?: string;
	district?: string;
	phone_number?: string;
	opening_hours?: string;
	is_favorite?: boolean;
	distance_km?: number;
	neighborhood?: string;
}

interface POIMarkerProps {
	poi: POI;
	onClick?: (poi: POI) => void;
	showPopup?: boolean;
}

// Get POI color based on type and properties
const getPOIColor = (poi: POI): string => {
	if (poi.is_favorite) return '#ff4444'; // Red for favorites

	// Color by POI type
	switch (poi.poi_type) {
		case 'official':
			return '#3b82f6'; // Blue for official
		case 'user_approved':
			return '#10b981'; // Green for approved
		case 'user_temp':
			return '#f59e0b'; // Orange for temporary
		default:
			return '#6b7280'; // Gray for unknown
	}
};

// Get POI category color using centralized color system
const getPOICategoryColor = (poi: POI): string => {
	return (
		getPOIColorFromSystem(poi.category, poi.subcategory) || getPOIColor(poi)
	);
};

// Create custom POI icon
const createPOIIcon = (poi: POI) => {
	const color = getPOICategoryColor(poi);
	const size = poi.is_favorite ? 28 : 24;
	const borderWidth = poi.is_favorite ? 3 : 2;

	return L.divIcon({
		className: 'custom-poi-icon',
		html: `
      <div style="
        width: ${size}px; 
        height: ${size}px; 
        border-radius: 50%; 
        background-color: ${color}; 
        border: ${borderWidth}px solid white; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.3); 
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: white;
        font-weight: bold;
      ">
        ${poi.is_favorite ? '❤️' : '📍'}
      </div>
    `,
		iconSize: [size, size],
		iconAnchor: [size / 2, size / 2],
		popupAnchor: [0, -size / 2],
	});
};

const POIMarker: React.FC<POIMarkerProps> = ({
	poi,
	onClick,
	showPopup = false,
}) => {
	// Validate coordinates
	if (
		typeof poi.latitude !== 'number' ||
		typeof poi.longitude !== 'number' ||
		isNaN(poi.latitude) ||
		isNaN(poi.longitude)
	) {
		console.warn('Invalid POI coordinates:', poi);
		return null;
	}

	const poiIcon = createPOIIcon(poi);

	const handleClick = () => {
		onClick?.(poi);
	};

	const formatDistance = (distanceKm: number): string => {
		if (distanceKm < 1) {
			return `${Math.round(distanceKm * 1000)}m away`;
		}
		return `${distanceKm.toFixed(1)}km away`;
	};

	return (
		<Marker
			position={[poi.latitude, poi.longitude]}
			icon={poiIcon}
			eventHandlers={{
				click: handleClick,
			}}>
			{showPopup && (
				<Popup>
					<div
						style={{
							fontFamily: 'system-ui',
							fontSize: '12px',
							minWidth: '200px',
						}}>
						<div
							style={{
								fontWeight: 'bold',
								marginBottom: '8px',
								fontSize: '14px',
							}}>
							{poi.name}
						</div>

						<div style={{ marginBottom: '4px' }}>
							<strong>Type:</strong>{' '}
							{poi.subcategory
								? `${poi.category} - ${poi.subcategory}`
								: poi.category}
						</div>

						{(poi.city || poi.district) && (
							<div style={{ marginBottom: '4px' }}>
								<strong>Location:</strong>{' '}
								{[poi.district, poi.city].filter(Boolean).join(', ')}
							</div>
						)}

						{poi.distance_km && (
							<div
								style={{
									marginBottom: '4px',
									color: '#3b82f6',
									fontWeight: 'bold',
								}}>
								{formatDistance(poi.distance_km)}
							</div>
						)}

						{poi.phone_number && (
							<div style={{ marginBottom: '4px' }}>
								<strong>Phone:</strong> {poi.phone_number}
							</div>
						)}

						{poi.opening_hours && (
							<div style={{ marginBottom: '4px' }}>
								<strong>Hours:</strong> {poi.opening_hours}
							</div>
						)}

						<div style={{ marginTop: '8px', fontSize: '11px', color: '#666' }}>
							{poi.latitude.toFixed(4)}°, {poi.longitude.toFixed(4)}°
						</div>

						{poi.is_favorite && (
							<div
								style={{
									marginTop: '4px',
									color: '#ff4444',
									fontSize: '11px',
								}}>
								❤️ Favorite
							</div>
						)}
					</div>
				</Popup>
			)}
		</Marker>
	);
};

export default POIMarker;
