/** @format */

'use client';

import { colors } from '@/app/colors';
import { useRouter } from 'next/navigation';
import {
	FaCog,
	FaComments,
	FaInfo,
	FaLocationArrow,
	FaMapMarkerAlt,
	FaSignOutAlt,
	FaSync,
	FaTimes,
	FaUser,
} from 'react-icons/fa';
import { FiZap } from 'react-icons/fi';

interface TopControlsProps {
	userLocation: {
		latitude: number;
		longitude: number;
		accuracy?: number;
		timestamp: number;
		source: 'auto' | 'manual';
		label?: string;
		liveLocationEnabled?: boolean;
	} | null;
	showSettings: boolean;
	setShowSettings: (show: boolean) => void;
	shouldFlashInfo: boolean;
	flashCount: number;
	showInfo: boolean;
	setShowInfo: (show: boolean) => void;
	setShowNavButtons: (show: boolean) => void;
	setShowBottomInstructions: (show: boolean) => void;
	setShowGlobeTitle: (show: boolean) => void;
	setShouldFlashInfo: (flash: boolean) => void;
	setFlashCount: (count: number) => void;
	setUserManuallyClickedInfo: (clicked: boolean) => void;
	handleGoToUserLocation: () => void;
	locationLoading: boolean;
	locationError: string | null;
	requestAutoLocation: () => void;
	formatLocation: () => string;
	handleLogout: () => Promise<void>;
	isFlattened?: boolean;
	onToggleView?: () => void;
}

export default function TopControls({
	userLocation,
	showSettings,
	setShowSettings,
	shouldFlashInfo,
	flashCount,
	showInfo,
	setShowInfo,
	setShowNavButtons,
	setShowBottomInstructions,
	setShowGlobeTitle,
	setShouldFlashInfo,
	setFlashCount,
	setUserManuallyClickedInfo,
	handleGoToUserLocation,
	locationLoading,
	locationError,
	requestAutoLocation,
	formatLocation,
	handleLogout,
	isFlattened = false,
	onToggleView,
}: TopControlsProps) {
	const router = useRouter();

	return (
		<div
			className='fixed left-6 z-[60] mt-20 rounded-2xl backdrop-blur-md shadow-xl flex items-center gap-4 px-6 py-3 border'
			style={{
				minWidth: 0,
				top: 0,
				background: colors.glass.backdrop,
				borderColor: colors.glass.medium,
				boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
			}}>
			<button
				onClick={() => router.back()}
				className='p-3 rounded-xl transition-all duration-300 backdrop-blur-md border'
				style={{
					background: colors.glass.light,
					color: colors.neutral.slateGray,
					borderColor: colors.glass.medium,
				}}
				onMouseEnter={(e) => {
					e.currentTarget.style.background = colors.glass.medium;
					e.currentTarget.style.transform = 'translateY(-1px)';
				}}
				onMouseLeave={(e) => {
					e.currentTarget.style.background = colors.glass.light;
					e.currentTarget.style.transform = 'translateY(0)';
				}}
				title='Back to Chat'>
				<FaTimes className='w-5 h-5' />
			</button>

			<button
				onClick={handleGoToUserLocation}
				className='p-3 rounded-xl transition-all duration-300 backdrop-blur-md border'
				style={{
					background: userLocation
						? colors.glass.greenGlass
						: colors.glass.dark,
					color: userLocation ? colors.brand.green : colors.neutral.slateGray,
					borderColor: colors.glass.medium,
					cursor: userLocation ? 'pointer' : 'not-allowed',
				}}
				onMouseEnter={(e) => {
					if (userLocation) {
						e.currentTarget.style.background = colors.glass.light;
						e.currentTarget.style.transform = 'translateY(-1px)';
					}
				}}
				onMouseLeave={(e) => {
					if (userLocation) {
						e.currentTarget.style.background = colors.glass.greenGlass;
						e.currentTarget.style.transform = 'translateY(0)';
					}
				}}
				title={userLocation ? 'Go to Your Location' : 'No location available'}
				disabled={!userLocation}>
				<FaLocationArrow className='w-5 h-5' />
			</button>

			{/* Flat Map Toggle Button */}
			{onToggleView && (
				<button
					onClick={onToggleView}
					className='p-3 rounded-xl transition-all duration-300 backdrop-blur-md border'
					style={{
						background: isFlattened
							? colors.glass.blueGlass
							: colors.glass.light,
						color: isFlattened ? colors.brand.blue : colors.neutral.slateGray,
						borderColor: colors.glass.medium,
					}}
					onMouseEnter={(e) => {
						e.currentTarget.style.background = colors.glass.medium;
						e.currentTarget.style.transform = 'translateY(-1px)';
					}}
					onMouseLeave={(e) => {
						e.currentTarget.style.background = isFlattened
							? colors.glass.blueGlass
							: colors.glass.light;
						e.currentTarget.style.transform = 'translateY(0)';
					}}
					title={isFlattened ? 'Switch to Globe View' : 'Switch to Flat Map'}>
					<FaMapMarkerAlt className='w-5 h-5' />
				</button>
			)}

			<button
				onClick={() => {
					setShowInfo(!showInfo);
					setShowNavButtons(true);
					setShowBottomInstructions(true);
					setShowGlobeTitle(true);
					setShouldFlashInfo(false);
					setFlashCount(0);
					setUserManuallyClickedInfo(!showInfo);
				}}
				className='p-3 rounded-xl transition-all duration-300 backdrop-blur-md border'
				style={
					shouldFlashInfo && flashCount % 2 === 1
						? {
								background: colors.brand.blue,
								color: colors.neutral.cloudWhite,
								borderColor: colors.brand.blue,
								boxShadow: `0 0 20px ${colors.brand.blue}50`,
						  }
						: {
								background: colors.glass.light,
								color: colors.neutral.slateGray,
								borderColor: colors.glass.medium,
						  }
				}
				onMouseEnter={(e) => {
					if (!(shouldFlashInfo && flashCount % 2 === 1)) {
						e.currentTarget.style.background = colors.glass.medium;
						e.currentTarget.style.transform = 'translateY(-1px)';
					}
				}}
				onMouseLeave={(e) => {
					if (!(shouldFlashInfo && flashCount % 2 === 1)) {
						e.currentTarget.style.background = colors.glass.light;
						e.currentTarget.style.transform = 'translateY(0)';
					}
				}}
				title='Toggle Info & Navigation'>
				<FaInfo className='w-5 h-5' />
			</button>
			<div className='relative settings-dropdown'>
				<button
					onClick={() => setShowSettings(!showSettings)}
					className='bg-white/80 text-slate-700 p-2 rounded-xl hover:bg-gray-100 transition-all duration-300 shadow-md'
					title='Settings'>
					<FaCog className='w-5 h-5' />
				</button>

				{showSettings && (
					<div className='absolute right-0 mt-2 w-80 bg-white/95 backdrop-blur-sm border border-slate-200 rounded-xl shadow-xl z-50 py-2 text-sm overflow-hidden'>
						{/* Location Display */}
						<div className='px-4 py-3 bg-slate-50 border-b border-slate-100'>
							<div className='flex items-start gap-3'>
								<FaMapMarkerAlt className='text-slate-600 mt-0.5 flex-shrink-0' />
								<div className='min-w-0 flex-1'>
									<div className='flex items-center justify-between mb-1'>
										<div className='flex items-center gap-2'>
											<span className='font-medium text-slate-800'>
												Your Location
											</span>
											{userLocation && (
												<div
													className='w-2 h-2 bg-green-500 rounded-full animate-pulse'
													title='Location active'></div>
											)}
										</div>
										<button
											onClick={requestAutoLocation}
											disabled={locationLoading}
											className='text-slate-600 hover:text-slate-800 disabled:opacity-50 transition-colors'
											title='Refresh location'>
											<FaSync
												className={`w-3 h-3 ${
													locationLoading ? 'animate-spin' : ''
												}`}
											/>
										</button>
									</div>
									<div className='text-xs text-slate-600 break-all'>
										{locationLoading ? (
											<span className='text-slate-500'>
												📍 Getting location...
											</span>
										) : locationError ? (
											<span className='text-red-600'>⚠️ {locationError}</span>
										) : (
											formatLocation()
										)}
									</div>
								</div>
							</div>
						</div>

						{/* Menu Items */}
						<button
							onClick={() => {
								setShowSettings(false);
								router.push('/profile');
							}}
							className='w-full flex items-center gap-3 px-4 py-3 hover:bg-slate-50 transition-colors text-slate-700 hover:text-slate-900'>
							<FaUser className='text-base' /> Profile
						</button>
						<button
							onClick={() => {
								setShowSettings(false);
								router.push('/credits');
							}}
							className='w-full flex items-center gap-3 px-4 py-3 hover:bg-slate-50 transition-colors text-slate-700 hover:text-slate-900'>
							<FiZap className='text-base' /> Credits
						</button>
						<button
							onClick={() => {
								setShowSettings(false);
								router.push('/settings');
							}}
							className='w-full flex items-center gap-3 px-4 py-3 hover:bg-slate-50 transition-colors text-slate-700 hover:text-slate-900'>
							<FaCog className='text-base' /> Settings
						</button>
						<div className='border-t border-slate-200 my-1' />
						<button
							onClick={handleLogout}
							className='w-full flex items-center gap-3 px-4 py-3 hover:bg-red-50 transition-colors text-red-600 hover:text-red-700'>
							<FaSignOutAlt className='text-base' /> Logout
						</button>
					</div>
				)}
			</div>
			<button
				onClick={() => router.push('/chat')}
				className='bg-white/80 text-slate-700 p-2 rounded-xl hover:bg-gray-100 transition-all duration-300 shadow-md'
				title='Go to Chat'>
				<FaComments className='w-5 h-5' />
			</button>
		</div>
	);
}
